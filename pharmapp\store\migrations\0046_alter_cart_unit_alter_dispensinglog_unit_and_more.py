# Generated by Django 5.1.7 on 2025-04-23 06:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0045_remove_stockadjustment_adjusted_quantity_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cart',
            name='unit',
            field=models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='dispensinglog',
            name='unit',
            field=models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='formulation',
            name='dosage_form',
            field=models.CharField(blank=True, choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Galenical', 'Galenical'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Solution', 'Solution'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal'), ('Detergent', 'Detergent'), ('Drinks', 'Drinks'), ('Paste', 'Paste'), ('Table-water', 'Table-water'), ('Food-item', 'Food-item'), ('Sweets', 'Sweets'), ('Soaps', 'Soaps'), ('Biscuits', 'Biscuits')], default='DosageForm', max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='item',
            name='dosage_form',
            field=models.CharField(blank=True, choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Galenical', 'Galenical'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Solution', 'Solution'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal'), ('Detergent', 'Detergent'), ('Drinks', 'Drinks'), ('Paste', 'Paste'), ('Table-water', 'Table-water'), ('Food-item', 'Food-item'), ('Sweets', 'Sweets'), ('Soaps', 'Soaps'), ('Biscuits', 'Biscuits')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='item',
            name='unit',
            field=models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='salesitem',
            name='unit',
            field=models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], default='unit', max_length=10),
        ),
        migrations.AlterField(
            model_name='storeitem',
            name='dosage_form',
            field=models.CharField(choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Galenical', 'Galenical'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Solution', 'Solution'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal'), ('Detergent', 'Detergent'), ('Drinks', 'Drinks'), ('Paste', 'Paste'), ('Table-water', 'Table-water'), ('Food-item', 'Food-item'), ('Sweets', 'Sweets'), ('Soaps', 'Soaps'), ('Biscuits', 'Biscuits')], default='dosage_form', max_length=255),
        ),
        migrations.AlterField(
            model_name='storeitem',
            name='unit',
            field=models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=100),
        ),
        migrations.AlterField(
            model_name='wholesalecart',
            name='unit',
            field=models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='wholesaleitem',
            name='dosage_form',
            field=models.CharField(blank=True, choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Galenical', 'Galenical'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Solution', 'Solution'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal'), ('Detergent', 'Detergent'), ('Drinks', 'Drinks'), ('Paste', 'Paste'), ('Table-water', 'Table-water'), ('Food-item', 'Food-item'), ('Sweets', 'Sweets'), ('Soaps', 'Soaps'), ('Biscuits', 'Biscuits')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='wholesaleitem',
            name='unit',
            field=models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='wholesalesalesitem',
            name='unit',
            field=models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], default='unit', max_length=10),
        ),
    ]

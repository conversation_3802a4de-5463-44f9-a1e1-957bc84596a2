{% extends "partials/base.html" %}
{% load humanize %}
{% block content %}
<style>
    .procurement-list {
        margin-top: 20px;
    }

    .procurement-list table {
        width: 100%;
        border-collapse: collapse;
    }

    .procurement-list th,
    .procurement-list td {
        padding: 10px;
        border: 1px solid #ddd;
        text-align: left;
    }

    .procurement-list th {
        background-color: #f2f2f2;
        font-weight: bold;
    }

    .procurement-list tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .procurement-list tr:hover {
        background-color: #f1f1f1;
    }

    .procurement-list .btn {
        margin-right: 5px;
    }

    .search-form {
        margin-bottom: 20px;
    }

    .search-form input {
        padding: 8px;
        width: 300px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .search-form button {
        padding: 8px 15px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .search-form button:hover {
        background-color: #0056b3;
    }

    .status-draft {
        color: #ff9800;
        font-weight: bold;
    }

    .status-completed {
        color: #4caf50;
        font-weight: bold;
    }
</style>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>Retail Procurement List</h2>
        <div>
            <a href="{% url 'store:add_procurement' %}" class="btn btn-primary btn-sm">New Procurement</a>
            <a href="{% url 'store:transfer_multiple_store_items' %}" class="btn btn-warning btn-sm">Transfer Items</a>
            <a href="{% url 'store:dashboard' %}" class="btn btn-secondary btn-sm">Dashboard</a>
        </div>
    </div>

    {% for message in messages %}
    <div class="alert alert-{{message.tags}} text-center">{{ message }}</div>
    {% endfor %}

    <div class="search-form">
        <form hx-get="{% url 'store:search_procurement' %}" hx-target="#procurement-list" hx-trigger="submit">
            <div class="input-group mb-3">
                <input type="text" name="name" class="form-control" placeholder="Search by supplier name...">
                <button class="btn btn-primary" type="submit">Search</button>
            </div>
        </form>
    </div>

    <div id="procurement-list" class="procurement-list">
        <div class="table-responsive">
            <table class="table table-striped table-hover table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>Supplier</th>
                        <th>Date</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for procurement in procurements %}
                    <tr>
                        <td>{{ procurement.id }}</td>
                        <td>
                            {% if procurement.supplier %}
                            {{ procurement.supplier.name|upper }}
                            {% else %}
                            OTHERS
                            {% endif %}
                        </td>
                        <td>{{ procurement.date }}</td>
                        <td>₦{{ procurement.calculated_total|default:procurement.total|floatformat:2|intcomma }}</td>
                        <td class="status-{{ procurement.status }}">{{ procurement.status|title }}</td>
                        <td>
                            <a href="{% url 'store:procurement_detail' procurement.id %}" class="btn btn-info btn-sm">View</a>
                            {% if procurement.status == 'draft' %}
                            <a href="{% url 'store:add_procurement' %}?draft_id={{ procurement.id }}" class="btn btn-warning btn-sm">Continue</a>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">No procurements found.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
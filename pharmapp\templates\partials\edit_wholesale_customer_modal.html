<style>
    .modal-header {
        background-color: #f8f9fa;
        padding: 10px 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .modal-header h3 {
        font-size: 20px;
        margin: 0;
    }

    .modal-body {
        padding: 50px;
    }

    .modal-footer {
        padding: 10px 15px;
        border-top: 1px solid #dee2e6;
    }

    .modal-body form {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .modal-body form .form-group {
        display: flex;
        align-items: center;
    }

    .modal-body form label {
        flex: 1;
        margin-bottom: 3px;
        font-weight: 700;
    }

    .modal-body form .form-control {
        flex: 2;
        padding: 5px 10px;
    }

    .modal-body form button {
        align-self: flex-end;
        margin-top: 10px;
    }

    .btn-sm {
        padding: 3px 10px;
    }

    .modal-footer .btn {
        margin-right: 5px;
    }

    .modal-footer .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }
</style>

<div class="modal-header">
    <h3 class="col-md-6 offset-md-3">ADIT CUSTOMER</h3>
    <button
        type="button"
        class="close"
        data-bs-dismiss="modal"
        aria-label="Close"
    >
        x
    </button>
</div>
<div class="modal-body">
    <form
        action="{% url 'wholesale:edit_wholesale_customer' customer.id %}"
        method="post"
    >
        {% csrf_token %}
        <div class="form-group">
            <label for="name">Customer Name:</label>
            <input
                type="text"
                name="name"
                class="form-control mb-3"
                value="{{ customer.name }}"
            />
        </div>

        <div class="form-group">
            <label for="phone">Phone:</label>
            <input
                type="number"
                name="phone"
                class="form-control mb-3"
                value="{{ customer.phone }}"
            />
        </div>
        <div class="form-group">
            <label for="address">Address:</label>
            <input
                type="text"
                name="address"
                class="form-control mb-3"
                value="{{ customer.address }}"
            />
        </div>
        <button type="submit" class="btn btn-success btn-sm">
            Save Changes
        </button>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">
        Close
    </button>
</div>

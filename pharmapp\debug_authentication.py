#!/usr/bin/env python
"""
Debug script to test actual authentication with real credentials.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import authenticate
from userauth.models import User, Profile
import getpass


def debug_authentication():
    """Debug the authentication process"""
    print("Authentication Debug Tool")
    print("=" * 50)
    
    # List all users with their details
    print("Available users:")
    users = User.objects.all()
    
    for i, user in enumerate(users, 1):
        print(f"{i:2d}. Mobile: {user.mobile}")
        print(f"    Username: {user.username}")
        print(f"    Active: {user.is_active}")
        print(f"    Staff: {user.is_staff}")
        print(f"    Superuser: {user.is_superuser}")
        
        if hasattr(user, 'profile') and user.profile:
            print(f"    Profile: {user.profile.full_name} ({user.profile.user_type})")
        else:
            print(f"    Profile: MISSING")
        
        # Check if password is set
        if user.password:
            print(f"    Password: Set (length: {len(user.password)})")
        else:
            print(f"    Password: NOT SET")
        
        print()
    
    # Test authentication with user input
    print("\nTesting Authentication:")
    print("Enter credentials to test (or press Enter to skip):")
    
    mobile = input("Mobile number: ").strip()
    if mobile:
        password = getpass.getpass("Password: ")
        
        print(f"\nTesting authentication for mobile: {mobile}")
        
        # Test with Django's authenticate function
        user = authenticate(username=mobile, password=password)
        
        if user:
            print("✅ Authentication SUCCESSFUL!")
            print(f"   User: {user.username} ({user.mobile})")
            print(f"   Active: {user.is_active}")
            if hasattr(user, 'profile') and user.profile:
                print(f"   Profile: {user.profile.full_name} ({user.profile.user_type})")
        else:
            print("❌ Authentication FAILED!")
            
            # Try to find the user manually
            try:
                user_obj = User.objects.get(mobile=mobile)
                print(f"   User exists: {user_obj.username} ({user_obj.mobile})")
                print(f"   Active: {user_obj.is_active}")
                
                # Check password manually
                if user_obj.check_password(password):
                    print("   Password check: ✅ CORRECT")
                    print("   Issue might be with user_can_authenticate or other factors")
                else:
                    print("   Password check: ❌ INCORRECT")
                    
            except User.DoesNotExist:
                print("   User does not exist with this mobile number")
    
    print("\n" + "=" * 50)
    print("Debug complete!")


if __name__ == "__main__":
    debug_authentication()

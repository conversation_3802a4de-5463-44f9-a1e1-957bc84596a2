# Generated by Django 5.1.7 on 2025-03-09 09:55

import django.db.models.deletion
import django.utils.timezone
import shortuuid.django_fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0001_initial'),
        ('store', '0030_alter_expensecategory_options'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='receipt',
            options={'ordering': ['-date']},
        ),
        migrations.AddField(
            model_name='receipt',
            name='has_returns',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='receipt',
            name='last_modified',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='receipt',
            name='return_notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='buyer_address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer.customer'),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='date',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='payment_method',
            field=models.CharField(choices=[('Cash', 'Cash'), ('Transfer', 'Transfer'), ('Wallet', 'Wallet')], default='Cash', max_length=20),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='receipt_id',
            field=shortuuid.django_fields.ShortUUIDField(alphabet=None, length=10, max_length=20, prefix='RID:', unique=True),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='sales',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='receipt', to='store.sales'),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='status',
            field=models.CharField(choices=[('Paid', 'Paid'), ('Unpaid', 'Unpaid')], default='Paid', max_length=20),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, max_digits=10),
        ),
    ]

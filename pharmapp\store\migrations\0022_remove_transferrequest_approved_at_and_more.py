# Generated by Django 5.1.5 on 2025-02-16 09:07

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0021_alter_item_exp_date_alter_wholesaleitem_exp_date'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='transferrequest',
            name='approved_at',
        ),
        migrations.RemoveField(
            model_name='transferrequest',
            name='completed_at',
        ),
        migrations.RemoveField(
            model_name='transferrequest',
            name='item',
        ),
        migrations.RemoveField(
            model_name='transferrequest',
            name='quantity',
        ),
        migrations.RemoveField(
            model_name='transferrequest',
            name='requested_at',
        ),
        migrations.AddField(
            model_name='transferrequest',
            name='approved_quantity',
            field=models.PositiveIntegerField(blank=True, help_text='Quantity approved (may be adjusted).', null=True),
        ),
        migrations.AddField(
            model_name='transferrequest',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime.now),
        ),
        migrations.AddField(
            model_name='transferrequest',
            name='requested_quantity',
            field=models.PositiveIntegerField(default=0, help_text='Quantity originally requested.'),
        ),
        migrations.AddField(
            model_name='transferrequest',
            name='retail_item',
            field=models.ForeignKey(blank=True, help_text='Set when request originates from retail side.', null=True, on_delete=django.db.models.deletion.CASCADE, to='store.item'),
        ),
        migrations.AlterField(
            model_name='transferrequest',
            name='from_wholesale',
            field=models.BooleanField(default=False, help_text='True if request initiated by wholesale; False if by retail.'),
        ),
        migrations.AlterField(
            model_name='transferrequest',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='transferrequest',
            name='wholesale_item',
            field=models.ForeignKey(blank=True, help_text='Set when request originates from wholesale side.', null=True, on_delete=django.db.models.deletion.CASCADE, to='store.wholesaleitem'),
        ),
    ]

# Generated by Django 5.1.7 on 2025-04-23 10:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('supplier', '0004_procurement_status_wholesaleprocurement_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='procurementitem',
            name='dosage_form',
            field=models.CharField(choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Galenical', 'Galenical'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Solution', 'Solution'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal'), ('Detergent', 'Detergent'), ('Drinks', 'Drinks'), ('Paste', 'Paste'), ('Table-water', 'Table-water'), ('Food-item', 'Food-item'), ('Sweets', 'Sweets'), ('Soaps', 'Soaps'), ('Biscuits', 'Biscuits')], default='dosage_form', max_length=255),
        ),
        migrations.AlterField(
            model_name='procurementitem',
            name='unit',
            field=models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Can', 'Can'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=100),
        ),
        migrations.AlterField(
            model_name='wholesaleprocurementitem',
            name='dosage_form',
            field=models.CharField(choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Galenical', 'Galenical'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Solution', 'Solution'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal'), ('Detergent', 'Detergent'), ('Drinks', 'Drinks'), ('Paste', 'Paste'), ('Table-water', 'Table-water'), ('Food-item', 'Food-item'), ('Sweets', 'Sweets'), ('Soaps', 'Soaps'), ('Biscuits', 'Biscuits')], default='dosage_form', max_length=255),
        ),
        migrations.AlterField(
            model_name='wholesaleprocurementitem',
            name='unit',
            field=models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Can', 'Can'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Sachets', 'Sachets'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=100),
        ),
    ]

# Generated by Django 5.1.5 on 2025-02-17 15:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0024_alter_transferrequest_from_wholesale_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StoreItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('brand', models.CharField(blank=True, default='None', max_length=255, null=True)),
                ('dosage_form', models.CharField(choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal')], default='dosage_form', max_length=255)),
                ('unit', models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=100)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('batch_number', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='store.supplier')),
            ],
        ),
    ]

{% block content %}
<style>
    .modal-header {
        background-color: #f8f9fa;
        padding: 10x 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .modal-header h3 {
        font-size: 18px;
        margin: 0;
    }

    .modal-body {
        padding: 50px;
    }

    .modal-footer {
        padding: 5px 10px;
        border-top: 1px solid #dee2e6;
    }

    .modal-body form {
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .modal-body form .form-group {
        display: flex;
        align-items: center;
    }

    .modal-body form label {
        flex: 1;
        margin-bottom: 1px;
        font-weight: 700;
    }

    .modal-body form .form-control {
        flex: 2;
        padding: 5px 10px;
    }

    .modal-body form button {
        align-self: flex-end;
        margin-top: 10px;
    }

    .btn-sm {
        padding: 3px 10px;
    }

    .modal-footer .btn {
        margin-right: 5px;
    }

    .modal-footer .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }
</style>


<div class="modal-header">
    <h3 class="col-md-6 offset-md-3">EDIT WHOLESALE ITEM</h3>
    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">x</button>
</div>
<div class="modal-body">
    <form action="{% url 'wholesale:edit_wholesale_item' item.id %}" method="post">
        {% csrf_token %}
        <div class="form-group">
            <label for="name">Item Name:</label>
            <input type="text" name="name" class="form-control mb-3" value="{{ item.name }}">
        </div>
        <div class="form-group">
            <label for="name">Dosage Form:</label>
            <input type="text" name="dosage_form" class="form-control mb-3" value="{{ item.dosage_form }}">
        </div>
        <div class="form-group">
            <label for="name">brand Name:</label>
            <input type="text" name="brand" class="form-control mb-3" value="{{ item.brand }}">
        </div>
        <div class="form-group">
            <label for="name">Item Unit:</label>
            <!-- Unit Selection Dropdown -->
            <select name="unit" class="form-control mt-3" required>
                <option value="Unit" {% if item.unit == 'Unit' %}selected{% endif %}>Select Unit</option>
                <option value="Pcs" {% if item.unit == 'Pcs' %}selected{% endif %}>Pieces</option>
                <option value="Pack" {% if item.unit == 'Pack' %}selected{% endif %}>Packets</option>
                <option value="Tab" {% if item.unit == 'Tab' %}selected{% endif %}>Tablets</option>
                <option value="Tin" {% if item.unit == 'Tin' %}selected{% endif %}>Tins</option>
                <option value="Caps" {% if item.unit == 'Caps' %}selected{% endif %}>Capsules</option>
                <option value="Carton" {% if item.unit == 'Carton' %}selected{% endif %}>Cartons</option>
                <option value="Card" {% if item.unit == 'Card' %}selected{% endif %}>Cards</option>
                <option value="Bottle" {% if item.unit == 'Bottle' %}selected{% endif %}>Bottles</option>
                <option value="Roll" {% if item.unit == 'Roll' %}selected{% endif %}>Rolls</option>
                <option value="Vail" {% if item.unit == 'Vail' %}selected{% endif %}>Vail</option>
                <option value="Amp" {% if item.unit == 'Amp' %}selected{% endif %}>Ample</option>
            </select>
        </div>
        <div class="form-group">
            <label for="cost">Cost Price:</label>
            <input type="number" step="0.01" name="cost" id="cost" class="form-control mb-3" value="{{ item.cost }}"
                oninput="calculatePrice()">
        </div>
        <div class="form-group">
            <!-- Markup Percentage Dropdown -->
            <select name="markup" id="markup" class="form-control mt-3" onchange="calculatePrice()">
                <option value="0" {% if item.markup == 0 %}selected{% endif %}>Select Markup</option>
                <option value="2.5" {% if item.markup == 2.5 %}selected{% endif %}>2.5% markup</option>
                <option value="5" {% if item.markup == 5 %}selected{% endif %}>5% markup</option>
                <option value="7.5" {% if item.markup == 7.5 %}selected{% endif %}>7.5% markup</option>
                <option value="10" {% if item.markup == 10 %}selected{% endif %}>10% markup</option>
                <option value="12.5" {% if item.markup == 12.5 %}selected{% endif %}>12.5% markup</option>
                <option value="15" {% if item.markup == 15 %}selected{% endif %}>15% markup</option>
                <option value="17.5" {% if item.markup == 17.5 %}selected{% endif %}>17.5% markup</option>
                <option value="20" {% if item.markup == 20 %}selected{% endif %}>20% markup</option>
                <option value="22.5" {% if item.markup == 22.5 %}selected{% endif %}>22.5% markup</option>
                <option value="25" {% if item.markup == 25 %}selected{% endif %}>25% markup</option>
                <option value="27.5" {% if item.markup == 27.5 %}selected{% endif %}>27.5% markup</option>
                <option value="30" {% if item.markup == 30 %}selected{% endif %}>30% markup</option>
                <option value="32.5" {% if item.markup == 32.5 %}selected{% endif %}>32.5% markup</option>
                <option value="35" {% if item.markup == 35 %}selected{% endif %}>35% markup</option>
                <option value="37.5" {% if item.markup == 37.5 %}selected{% endif %}>37.5% markup</option>
                <option value="40" {% if item.markup == 40 %}selected{% endif %}>40% markup</option>
                <option value="42.5" {% if item.markup == 42.5 %}selected{% endif %}>42.5% markup</option>
                <option value="45" {% if item.markup == 45 %}selected{% endif %}>45% markup</option>
                <option value="47.5" {% if item.markup == 47.5 %}selected{% endif %}>47.5% markup</option>
                <option value="50" {% if item.markup == 50 %}selected{% endif %}>50% markup</option>
                <option value="52.5" {% if item.markup == 52.5 %}selected{% endif %}>52.5% markup</option>
                <option value="55" {% if item.markup == 55 %}selected{% endif %}>55% markup</option>
                <option value="57.5" {% if item.markup == 57.5 %}selected{% endif %}>57.5% markup</option>
                <option value="60" {% if item.markup == 60 %}selected{% endif %}>60% markup</option>
                <option value="62.5" {% if item.markup == 62.5 %}selected{% endif %}>62.5% markup</option>
                <option value="65" {% if item.markup == 65 %}selected{% endif %}>65% markup</option>
                <option value="67.5" {% if item.markup == 67.5 %}selected{% endif %}>67.5% markup</option>
                <option value="70" {% if item.markup == 70 %}selected{% endif %}>70% markup</option>
                <option value="72.5" {% if item.markup == 72.5 %}selected{% endif %}>72.5% markup</option>
                <option value="75" {% if item.markup == 75 %}selected{% endif %}>75% markup</option>
                <option value="77.5" {% if item.markup == 77.5 %}selected{% endif %}>77.5% markup</option>
                <option value="80" {% if item.markup == 80 %}selected{% endif %}>80% markup</option>
                <option value="82.5" {% if item.markup == 82.5 %}selected{% endif %}>82.5% markup</option>
                <option value="85" {% if item.markup == 85 %}selected{% endif %}>85% markup</option>
                <option value="87.5" {% if item.markup == 87.5 %}selected{% endif %}>87.5% markup</option>
                <option value="90" {% if item.markup == 90 %}selected{% endif %}>90% markup</option>
                <option value="92.5" {% if item.markup == 92.5 %}selected{% endif %}>92.5% markup</option>
                <option value="95" {% if item.markup == 95 %}selected{% endif %}>95% markup</option>
                <option value="97.5" {% if item.markup == 97.5 %}selected{% endif %}>97.5% markup</option>
                <option value="100" {% if item.markup == 100 %}selected{% endif %}>100% markup</option>
            </select>
        </div>

        <div class="form-group">
            <label for="price">Selling Price:</label>
            <input type="number" step="0.01" name="price" id="price" class="form-control mb-3" value="{{ item.price }}">
        </div>
        <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="manualPriceOverride" name="manual_price_override">
            <label class="form-check-label" for="manualPriceOverride">
                Manually override selling price
            </label>
        </div>
        <div class="form-group">
            <label for="stock">Stock Qty:</label>
            <input type="number" name="stock" class="form-control mb-3" value="{{ item.stock }}">
        </div>
        <div class="form-group">
            <label for="expiry">Exp-date:</label>
            <input type="date" name="exp_date" class="form-control mb-3" value="{{ item.exp_date|date:'Y-m-d' }}">
        </div>
        <button type="submit" class="btn btn-success btn-sm">Save Changes</button>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
</div>

<script>
    // Get references to the elements
    const costInput = document.getElementById("cost");
    const markupSelect = document.getElementById("markup");
    const priceInput = document.getElementById("price");
    const manualOverrideCheckbox = document.getElementById("manualPriceOverride");

    // Check if the current price matches the calculated price
    function checkIfManualPrice() {
        const cost = parseFloat(costInput.value) || 0;
        const markupPercentage = parseFloat(markupSelect.value) || 0;
        const currentPrice = parseFloat(priceInput.value) || 0;

        // Calculate what the price should be based on cost and markup
        const calculatedPrice = cost + (cost * markupPercentage / 100);

        // If the prices don't match (allowing for small floating point differences), it's a manual price
        return Math.abs(calculatedPrice - currentPrice) > 0.01;
    }

    // Set initial state based on whether the price appears to be manually set
    let manualPriceOverride = checkIfManualPrice();
    manualOverrideCheckbox.checked = manualPriceOverride;

    // Function to calculate price based on cost and markup
    function calculatePrice() {
        if (manualPriceOverride) return; // Skip calculation if manual override is enabled

        const cost = parseFloat(costInput.value) || 0;
        const markupPercentage = parseFloat(markupSelect.value) || 0;

        // Calculate price based on the cost and markup percentage
        const price = cost + (cost * markupPercentage / 100);

        // Set the calculated price in the price input field
        priceInput.value = price.toFixed(2);
    }

    // Add event listener for the manual override checkbox
    manualOverrideCheckbox.addEventListener('change', function() {
        manualPriceOverride = this.checked;

        // If manual override is disabled, recalculate the price
        if (!manualPriceOverride) {
            calculatePrice();
        }
    });

    // Add event listeners for cost and markup changes
    costInput.addEventListener('input', calculatePrice);
    markupSelect.addEventListener('change', calculatePrice);
</script>
{% endblock %}
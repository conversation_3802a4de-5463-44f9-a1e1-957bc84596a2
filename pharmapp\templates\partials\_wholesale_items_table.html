{% for item in wholesale_items %}
<tr id="wholesale-item-{{ item.id }}">
  <td>
    <input type="checkbox" name="select_{{ item.id }}" class="item-checkbox">
  </td>
  <td>{{ item.name|upper }}</td>
  <td>{{ item.dosage_form }}</td>
  <td>{{ item.brand }}</td>
  <td>{{ item.unit }}</td>
  <td>{{ item.stock }}</td>
  <td>
    <input type="number" name="quantity_{{ item.id }}" style="width:100px;" class="form-control" min="0" value="0">
  </td>
  <td>
    <select name="transfer_unit_{{ item.id }}" style="width:130px;" class="form-control unit-select" data-item-id="{{ item.id }}">
      <option value="{{ item.unit }}" selected>{{ item.unit }}</option>
      {% for unit_value, unit_display in unit_choices %}
        {% if unit_value != item.unit %}
          <option value="{{ unit_value }}">{{ unit_display }}</option>
        {% endif %}
      {% endfor %}
    </select>
  </td>
  <td>
    <input type="number" name="unit_conversion_{{ item.id }}" style="width:100px;" class="form-control unit-conversion" min="1" value="1" step="0.01">
    <small class="text-muted conversion-label">1 {{ item.unit }} = <span class="conversion-value">1</span> <span class="target-unit">{{ item.unit }}</span></small>
  </td>
  <td>
    <select name="markup_{{ item.id }}" style="width:150px;" class="form-control">
      <option value="0">Select Markup</option>
      <option value="2.5">2.5% markup</option>
      <option value="5">5% markup</option>
      <option value="7.5">7.5% markup</option>
      <option value="10">10% markup</option>
      <option value="12.5">12.5% markup</option>
      <option value="15">15% markup</option>
      <option value="17.5">17.5% markup</option>
      <option value="20">20% markup</option>
      <option value="22.5">22.5% markup</option>
      <option value="25">25% markup</option>
      <option value="27.5">27.5% markup</option>
      <option value="30">30% markup</option>
      <option value="32.5">32.5% markup</option>
      <option value="35">35% markup</option>
      <option value="37.5">37.5% markup</option>
      <option value="40">40% markup</option>
      <option value="42.5">42.5% markup</option>
      <option value="45">45% markup</option>
      <option value="47.5">47.5% markup</option>
      <option value="50">50% markup</option>
      <option value="52.5">52.5% markup</option>
      <option value="55">55% markup</option>
      <option value="57.5">57.5% markup</option>
      <option value="60">60% markup</option>
      <option value="62.5">62.5% markup</option>
      <option value="65">65% markup</option>
      <option value="67.5">67.5% markup</option>
      <option value="70">70% markup</option>
      <option value="72.5">72.5% markup</option>
      <option value="75">75% markup</option>
      <option value="77.5">77.5% markup</option>
      <option value="80">80% markup</option>
      <option value="82.5">82.5% markup</option>
      <option value="85">85% markup</option>
      <option value="87.5">87.5% markup</option>
      <option value="90">90% markup</option>
      <option value="92.5">92.5% markup</option>
      <option value="95">95% markup</option>
      <option value="97.5">97.5% markup</option>
      <option value="100">100% markup</option>
    </select>
  </td>
  <td>
    <select name="destination_{{ item.id }}" class="form-control" style="width:130px;">
      <option value="">Select</option>
      <option value="retail">Retail</option>
      <option value="store">Store</option>
    </select>
  </td>
</tr>
{% endfor %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Update the select-all checkbox state based on the loaded items
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');

    if (selectAllCheckbox) {
      // Add event listener to the "Select All" checkbox
      selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;

        // Update all item checkboxes
        itemCheckboxes.forEach(function(checkbox) {
          checkbox.checked = isChecked;
        });
      });

      // Add event listeners to individual checkboxes to update "Select All" state
      itemCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', updateSelectAllCheckbox);
      });

      // Initial update of the select-all checkbox state
      updateSelectAllCheckbox();
    }

    // Function to update the "Select All" checkbox based on individual checkboxes
    function updateSelectAllCheckbox() {
      const allChecked = Array.from(itemCheckboxes).every(checkbox => checkbox.checked);
      const someChecked = Array.from(itemCheckboxes).some(checkbox => checkbox.checked);

      selectAllCheckbox.checked = allChecked;
      selectAllCheckbox.indeterminate = someChecked && !allChecked;
    }
  });
</script>
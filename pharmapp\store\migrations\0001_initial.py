# Generated by Django 5.1.5 on 2025-02-02 08:34

import datetime
import django.db.models.deletion
import shortuuid.django_fields
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customer', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Formulation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dosage_form', models.CharField(blank=True, choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal')], default='DosageForm', max_length=200, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('dosage_form', models.CharField(blank=True, choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal')], max_length=200, null=True)),
                ('brand', models.CharField(blank=True, max_length=200, null=True)),
                ('unit', models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=200, null=True)),
                ('cost', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('markup', models.DecimalField(choices=[(0, 'No markup'), (2.5, '2.5% markup'), (5, '5% markup'), (7.5, '7.5% markup'), (10, '10% markup'), (12.5, '12.5% markup'), (15, '15% markup'), (17.5, '17.5% markup'), (20, '20% markup'), (22.5, '22.5% markup'), (25, '25% markup'), (27.5, '27.5% markup'), (30, '30% markup'), (32.5, '32.5% markup'), (35, '35% markup'), (37.5, '37.5% markup'), (40, '40% markup'), (42.5, '42.5% markup'), (45, '45% markup'), (47.5, '47.5% markup'), (50, '50% markup'), (57.5, '57.5% markup'), (60, '60% markup'), (62.5, '62.5% markup'), (65, '65% markup'), (67.5, '67.5% markup'), (70, '70% markup'), (72.0, '72.% markup'), (75, '75% markup'), (77.5, '77.5% markup'), (80, '80% markup'), (82.5, '82.% markup'), (85, '85% markup'), (87.5, '87.5% markup'), (90, '90% markup'), (92.0, '92.% markup'), (95, '95% markup'), (97.5, '97.5% markup'), (100, '100% markup')], decimal_places=2, default=0, max_digits=6)),
                ('stock', models.PositiveIntegerField(blank=True, default=0, null=True)),
                ('low_stock_threshold', models.PositiveIntegerField(blank=True, default=0, null=True)),
                ('exp_date', models.DateField()),
            ],
            options={
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('phone', models.CharField(blank=True, max_length=15, null=True)),
                ('contact_info', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('dosage_form', models.CharField(blank=True, choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal')], max_length=200, null=True)),
                ('brand', models.CharField(blank=True, max_length=200, null=True)),
                ('unit', models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=200, null=True)),
                ('cost', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('markup', models.DecimalField(choices=[(0, 'No markup'), (2.5, '2.5% markup'), (5, '5% markup'), (7.5, '7.5% markup'), (10, '10% markup'), (12.5, '12.5% markup'), (15, '15% markup'), (17.5, '17.5% markup'), (20, '20% markup'), (22.5, '22.5% markup'), (25, '25% markup'), (27.5, '27.5% markup'), (30, '30% markup'), (32.5, '32.5% markup'), (35, '35% markup'), (37.5, '37.5% markup'), (40, '40% markup'), (42.5, '42.5% markup'), (45, '45% markup'), (47.5, '47.5% markup'), (50, '50% markup'), (57.5, '57.5% markup'), (60, '60% markup'), (62.5, '62.5% markup'), (65, '65% markup'), (67.5, '67.5% markup'), (70, '70% markup'), (72.0, '72.% markup'), (75, '75% markup'), (77.5, '77.5% markup'), (80, '80% markup'), (82.5, '82.% markup'), (85, '85% markup'), (87.5, '87.5% markup'), (90, '90% markup'), (92.0, '92.% markup'), (95, '95% markup'), (97.5, '97.5% markup'), (100, '100% markup')], decimal_places=2, default=0, max_digits=6)),
                ('stock', models.PositiveIntegerField(blank=True, default=0, null=True)),
                ('low_stock_threshold', models.PositiveIntegerField(blank=True, default=0, null=True)),
                ('exp_date', models.DateField()),
            ],
            options={
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='DispensingLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('brand', models.CharField(blank=True, max_length=100, null=True)),
                ('unit', models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=10, null=True)),
                ('quantity', models.PositiveIntegerField(default=0)),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('status', models.CharField(choices=[('Returned', 'Returned'), ('Partially Returned', 'Partially Returned'), ('Dispensed', 'Dispensed')], default='Dispensed', max_length=20)),
                ('created_at', models.DateTimeField(default=datetime.datetime.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('dosage_form', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.formulation')),
            ],
        ),
        migrations.CreateModel(
            name='Cart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('brand', models.CharField(blank=True, max_length=200, null=True)),
                ('unit', models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=200, null=True)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('cart_id', shortuuid.django_fields.ShortUUIDField(alphabet='1234567890', length=5, max_length=50, prefix='CID: ', unique=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('dosage_form', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.formulation')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.item')),
            ],
        ),
        migrations.CreateModel(
            name='ItemSelectionHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField()),
                ('action', models.CharField(choices=[('purchase', 'Purchase'), ('return', 'Return')], max_length=20)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customer')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.item')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Sales',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('date', models.DateField(default=datetime.datetime.now)),
                ('customer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer.customer')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('wholesale_customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.wholesalecustomer')),
            ],
        ),
        migrations.CreateModel(
            name='Receipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('buyer_name', models.CharField(blank=True, max_length=255, null=True)),
                ('buyer_address', models.CharField(blank=True, max_length=255, null=True)),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.0'), max_digits=10)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('receipt_id', shortuuid.django_fields.ShortUUIDField(alphabet='1234567890', length=5, max_length=50, prefix='', unique=True)),
                ('printed', models.BooleanField(default=False)),
                ('payment_method', models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer')], default='Cash', max_length=20)),
                ('status', models.CharField(choices=[('Paid', 'Paid'), ('Unpaid', 'Unpaid')], default='Unpaid', max_length=20)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.customer')),
                ('sales', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='receipts', to='store.sales')),
            ],
        ),
        migrations.CreateModel(
            name='SalesItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unit', models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], default='unit', max_length=10)),
                ('brand', models.CharField(blank=True, default='None', max_length=225, null=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity', models.IntegerField()),
                ('dosage_form', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.formulation')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.item')),
                ('sales', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_items', to='store.sales')),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleCart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('brand', models.CharField(blank=True, max_length=200, null=True)),
                ('unit', models.CharField(blank=True, choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=200, null=True)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('cart_id', shortuuid.django_fields.ShortUUIDField(alphabet='1234567890', length=5, max_length=50, prefix='CID: ', unique=True)),
                ('dosage_form', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.formulation')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.wholesaleitem')),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleReceipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('buyer_name', models.CharField(blank=True, max_length=255, null=True)),
                ('buyer_address', models.CharField(blank=True, max_length=255, null=True)),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.0'), max_digits=10)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('receipt_id', shortuuid.django_fields.ShortUUIDField(alphabet='1234567890', length=5, max_length=50, prefix='', unique=True)),
                ('payment_method', models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer')], default='Cash', max_length=20)),
                ('status', models.CharField(choices=[('Paid', 'Paid'), ('Unpaid', 'Unpaid')], default='Unpaid', max_length=20)),
                ('sales', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='wholesale_receipts', to='store.sales')),
                ('wholesale_customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.wholesalecustomer')),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleSalesItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('brand', models.CharField(blank=True, default='None', max_length=225, null=True)),
                ('unit', models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], default='unit', max_length=10)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity', models.IntegerField()),
                ('dosage_form', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.formulation')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.wholesaleitem')),
                ('sales', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wholesale_sales_items', to='store.sales')),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleSelectionHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField()),
                ('action', models.CharField(choices=[('purchase', 'Purchase'), ('return', 'Return')], max_length=20)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.wholesaleitem')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('wholesale_customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.wholesalecustomer')),
            ],
        ),
    ]

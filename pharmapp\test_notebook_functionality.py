#!/usr/bin/env python
"""
Test script to verify notebook functionality
Run this script to test the basic notebook features
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from notebook.models import Note, NoteCategory
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

def test_notebook_functionality():
    print("🧪 Testing Notebook Functionality")
    print("=" * 50)
    
    # Test 1: Check if models are working
    print("\n1. Testing Models...")
    try:
        # Check categories
        categories = NoteCategory.objects.all()
        print(f"   ✅ Found {categories.count()} categories")
        for cat in categories[:3]:
            print(f"      - {cat.name} ({cat.color})")
        
        # Create test user if doesn't exist
        test_user, created = User.objects.get_or_create(
            username='notebook_test_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        if created:
            test_user.set_password('testpass123')
            test_user.save()
            print(f"   ✅ Created test user: {test_user.username}")
        else:
            print(f"   ✅ Using existing test user: {test_user.username}")
            
    except Exception as e:
        print(f"   ❌ Model test failed: {e}")
        return False
    
    # Test 2: Create a test note
    print("\n2. Testing Note Creation...")
    try:
        # Get a category
        work_category = NoteCategory.objects.filter(name='Work').first()
        
        # Create test note
        test_note = Note.objects.create(
            title='Test Note for Notebook Feature',
            content='This is a test note to verify the notebook functionality is working correctly.',
            user=test_user,
            category=work_category,
            priority='high',
            tags='test, notebook, functionality',
            is_pinned=True,
            reminder_date=timezone.now() + timedelta(days=1)
        )
        print(f"   ✅ Created test note: {test_note.title}")
        print(f"      - ID: {test_note.id}")
        print(f"      - Category: {test_note.category.name if test_note.category else 'None'}")
        print(f"      - Priority: {test_note.get_priority_display()}")
        print(f"      - Tags: {test_note.get_tags_list()}")
        print(f"      - Pinned: {test_note.is_pinned}")
        print(f"      - Reminder: {test_note.reminder_date}")
        
    except Exception as e:
        print(f"   ❌ Note creation failed: {e}")
        return False
    
    # Test 3: Test note methods
    print("\n3. Testing Note Methods...")
    try:
        # Test tags
        tags = test_note.get_tags_list()
        print(f"   ✅ Tags method: {tags}")
        
        # Test priority badge
        badge_class = test_note.get_priority_badge_class()
        print(f"   ✅ Priority badge class: {badge_class}")
        
        # Test overdue check
        is_overdue = test_note.is_overdue()
        print(f"   ✅ Is overdue: {is_overdue}")
        
        # Test string representation
        note_str = str(test_note)
        print(f"   ✅ String representation: {note_str}")
        
    except Exception as e:
        print(f"   ❌ Note methods test failed: {e}")
        return False
    
    # Test 4: Test queries
    print("\n4. Testing Database Queries...")
    try:
        # Count user notes
        user_notes = Note.objects.filter(user=test_user)
        print(f"   ✅ User notes count: {user_notes.count()}")
        
        # Test pinned notes
        pinned_notes = Note.objects.filter(user=test_user, is_pinned=True)
        print(f"   ✅ Pinned notes count: {pinned_notes.count()}")
        
        # Test category filter
        work_notes = Note.objects.filter(user=test_user, category=work_category)
        print(f"   ✅ Work category notes: {work_notes.count()}")
        
        # Test search functionality
        search_results = Note.objects.filter(
            user=test_user,
            title__icontains='test'
        )
        print(f"   ✅ Search results for 'test': {search_results.count()}")
        
    except Exception as e:
        print(f"   ❌ Query test failed: {e}")
        return False
    
    # Test 5: Test URL patterns
    print("\n5. Testing URL Patterns...")
    try:
        from django.urls import reverse
        
        urls_to_test = [
            'notebook:dashboard',
            'notebook:note_list',
            'notebook:note_create',
            'notebook:category_list',
            'notebook:category_create',
        ]
        
        for url_name in urls_to_test:
            try:
                url = reverse(url_name)
                print(f"   ✅ {url_name}: {url}")
            except Exception as e:
                print(f"   ❌ {url_name}: {e}")
                return False
                
        # Test note detail URL
        note_detail_url = reverse('notebook:note_detail', kwargs={'pk': test_note.pk})
        print(f"   ✅ note_detail: {note_detail_url}")
        
    except Exception as e:
        print(f"   ❌ URL test failed: {e}")
        return False
    
    # Test 6: Clean up
    print("\n6. Cleaning Up...")
    try:
        # Delete test note
        test_note.delete()
        print(f"   ✅ Deleted test note")
        
        # Optionally delete test user (commented out to preserve)
        # test_user.delete()
        # print(f"   ✅ Deleted test user")
        
    except Exception as e:
        print(f"   ⚠️  Cleanup warning: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed! Notebook functionality is working correctly.")
    print("\nNext steps:")
    print("1. Start the Django server: python manage.py runserver")
    print("2. Login to the application")
    print("3. Navigate to the 'Notebook' section in the sidebar")
    print("4. Create your first note!")
    
    return True

if __name__ == '__main__':
    success = test_notebook_functionality()
    sys.exit(0 if success else 1)

#!/usr/bin/env python
"""
Test script to verify the login system is working correctly.
Run this with: python test_login_system.py
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import authenticate
from userauth.models import User, Profile


def test_authentication():
    """Test the authentication system"""
    print("Testing Authentication System")
    print("=" * 50)
    
    # List all users
    users = User.objects.all()
    print(f"Total users in system: {users.count()}")
    
    for user in users:
        print(f"\nUser: {user.mobile} ({user.username})")
        print(f"  - Active: {user.is_active}")
        print(f"  - Staff: {user.is_staff}")
        print(f"  - Superuser: {user.is_superuser}")
        
        # Check if user has profile
        if hasattr(user, 'profile') and user.profile:
            print(f"  - Profile: {user.profile.full_name} ({user.profile.user_type})")
        else:
            print("  - Profile: MISSING")
            
        # Test authentication (you'll need to know a password to test this)
        # For security, we won't test actual passwords here
        print(f"  - Can authenticate: Ready for testing")
    
    print("\n" + "=" * 50)
    print("Authentication system check complete!")
    
    # Check for users without profiles
    users_without_profiles = []
    for user in User.objects.all():
        try:
            profile = user.profile
        except:
            users_without_profiles.append(user)
    
    if users_without_profiles:
        print(f"\nWARNING: {len(users_without_profiles)} users without profiles found:")
        for user in users_without_profiles:
            print(f"  - {user.mobile} ({user.username})")
        print("\nRun: python manage.py fix_user_profiles")
    else:
        print("\nAll users have profiles ✓")


if __name__ == "__main__":
    test_authentication()

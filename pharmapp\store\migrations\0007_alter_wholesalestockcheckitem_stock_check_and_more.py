# Generated by Django 5.1.5 on 2025-02-08 07:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0006_wholesalestockcheck_wholesalestockcheckitem_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='wholesalestockcheckitem',
            name='stock_check',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wholesale_items', to='store.wholesalestockcheck'),
        ),
        migrations.CreateModel(
            name='BulkStockAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('processed_at', models.DateTimeField(auto_now_add=True)),
                ('stock_check', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.stockcheck')),
            ],
        ),
        migrations.CreateModel(
            name='BulkStockAdjustmentItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjusted_quantity', models.PositiveIntegerField()),
                ('processed', models.BooleanField(default=False)),
                ('bulk_adjustment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.bulkstockadjustment')),
                ('stock_check_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.stockcheckitem')),
            ],
        ),
    ]

# Generated by Django 5.1.7 on 2025-04-24 14:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('userauth', '0003_alter_profile_user_type'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='activitylog',
            options={'ordering': ['-timestamp'], 'verbose_name': 'Activity Log', 'verbose_name_plural': 'Activity Logs'},
        ),
        migrations.AddField(
            model_name='activitylog',
            name='action_type',
            field=models.CharField(choices=[('LOGIN', 'Login'), ('LOGOUT', 'Logout'), ('CREATE', 'Create'), ('UPDATE', 'Update'), ('DELETE', 'Delete'), ('VIEW', 'View'), ('EXPORT', 'Export'), ('IMPORT', 'Import'), ('TRANSFER', 'Transfer'), ('PAYMENT', 'Payment'), ('OTHER', 'Other')], default='OTHER', max_length=20),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='target_id',
            field=models.CharField(blank=True, help_text='The ID of the affected object', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='target_model',
            field=models.CharField(blank=True, help_text='The model affected by this action', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='user_agent',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='activitylog',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='activitylog',
            index=models.Index(fields=['user'], name='userauth_ac_user_id_190f03_idx'),
        ),
        migrations.AddIndex(
            model_name='activitylog',
            index=models.Index(fields=['timestamp'], name='userauth_ac_timesta_b4a7f3_idx'),
        ),
        migrations.AddIndex(
            model_name='activitylog',
            index=models.Index(fields=['action_type'], name='userauth_ac_action__e17430_idx'),
        ),
    ]

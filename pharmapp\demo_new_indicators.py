#!/usr/bin/env python
"""
Demo script to showcase new note indicators
Creates notes with different timestamps to demonstrate the indicators
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from notebook.models import Note, NoteCategory
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

def create_indicator_demo():
    print("🔔 Creating New Note Indicators Demo")
    print("=" * 45)
    
    # Get demo user
    try:
        demo_user = User.objects.get(mobile='9876543210')
        print(f"✅ Using demo user: {demo_user.username}")
    except User.DoesNotExist:
        print("❌ Demo user not found. Please run demo_notebook_features.py first.")
        return False
    
    # Clean up any existing indicator demo notes
    Note.objects.filter(user=demo_user, title__startswith='[DEMO]').delete()
    
    # Get a category
    work_category = NoteCategory.objects.filter(name='Work').first()
    
    # Create notes with different timestamps to demonstrate indicators
    
    # 1. Brand new note (just created)
    print("\n1. Creating brand new note...")
    new_note = Note.objects.create(
        title='[DEMO] Brand New Note - Just Created!',
        content='''This note was just created and should show a "NEW" indicator.
        
Features demonstrated:
- Green "NEW" badge
- Pulsing animation
- Shows in dashboard statistics
- Appears in sidebar notification count''',
        user=demo_user,
        category=work_category,
        priority='high',
        tags='demo, new, indicator, fresh',
        is_pinned=True
    )
    print(f"   ✅ Created: {new_note.title}")
    print(f"   ✅ Is new: {new_note.is_new()}")
    
    # 2. Recently updated note
    print("\n2. Creating recently updated note...")
    # Create an older note first
    old_timestamp = timezone.now() - timedelta(hours=12)
    updated_note = Note.objects.create(
        title='[DEMO] Recently Updated Note',
        content='This note was created 12 hours ago but updated recently.',
        user=demo_user,
        category=work_category,
        priority='medium',
        tags='demo, updated, indicator'
    )
    # Set old creation time
    updated_note.created_at = old_timestamp
    updated_note.save()
    
    # Now update it (this will set updated_at to now)
    updated_note.content = '''This note was created 12 hours ago but just updated!
    
Features demonstrated:
- Blue "UPDATED" badge
- Pulsing animation
- Shows in recently updated statistics
- Different from new notes'''
    updated_note.save()
    
    print(f"   ✅ Created and updated: {updated_note.title}")
    print(f"   ✅ Is new: {updated_note.is_new()}")
    print(f"   ✅ Is recently updated: {updated_note.is_recently_updated()}")
    
    # 3. Old note (no indicators)
    print("\n3. Creating old note...")
    old_timestamp = timezone.now() - timedelta(days=3)
    old_note = Note.objects.create(
        title='[DEMO] Old Note - No Indicators',
        content='''This note is 3 days old and should not show any indicators.
        
This demonstrates:
- No "NEW" or "UPDATED" badges
- Normal appearance
- Not counted in new activity statistics''',
        user=demo_user,
        priority='low',
        tags='demo, old, no-indicator'
    )
    # Set old timestamps
    old_note.created_at = old_timestamp
    old_note.updated_at = old_timestamp
    old_note.save()
    
    print(f"   ✅ Created: {old_note.title}")
    print(f"   ✅ Is new: {old_note.is_new()}")
    print(f"   ✅ Is recently updated: {old_note.is_recently_updated()}")
    
    # 4. Create a few more new notes for better demo
    print("\n4. Creating additional new notes...")
    for i in range(3):
        note = Note.objects.create(
            title=f'[DEMO] New Note #{i+1}',
            content=f'This is demo new note number {i+1} to show multiple new indicators.',
            user=demo_user,
            priority='medium',
            tags=f'demo, new, batch-{i+1}'
        )
        print(f"   ✅ Created: {note.title}")
    
    # Display statistics
    print("\n📊 Demo Statistics:")
    user_notes = Note.objects.filter(user=demo_user)
    
    # Calculate new notes (last 24 hours)
    new_notes_cutoff = timezone.now() - timedelta(hours=24)
    new_notes_count = user_notes.filter(created_at__gte=new_notes_cutoff, is_archived=False).count()
    
    # Calculate recently updated (last 6 hours, excluding new)
    recent_update_cutoff = timezone.now() - timedelta(hours=6)
    recently_updated_count = user_notes.filter(
        updated_at__gte=recent_update_cutoff,
        created_at__lt=recent_update_cutoff,
        is_archived=False
    ).count()
    
    print(f"   📝 Total notes: {user_notes.count()}")
    print(f"   🆕 New notes (24h): {new_notes_count}")
    print(f"   📝 Recently updated (6h): {recently_updated_count}")
    print(f"   🔔 Total new activity: {new_notes_count + recently_updated_count}")
    
    # Show demo notes with their indicator status
    print("\n🎭 Demo Notes Created:")
    demo_notes = Note.objects.filter(user=demo_user, title__startswith='[DEMO]').order_by('-created_at')
    for note in demo_notes:
        indicators = []
        if note.is_new():
            indicators.append("🆕 NEW")
        if note.is_recently_updated():
            indicators.append("📝 UPDATED")
        if not indicators:
            indicators.append("📄 NORMAL")
        
        print(f"   {' '.join(indicators)} - {note.title}")
    
    print("\n🎉 New Note Indicators Demo Created!")
    print("\nTo see the indicators in action:")
    print("1. Go to http://127.0.0.1:8000")
    print("2. Login with mobile: 9876543210, password: demo123")
    print("3. Navigate to Notebook > Dashboard")
    print("4. Check the statistics cards for new activity counts")
    print("5. Go to Notebook > All Notes to see the badges")
    print("6. Look for:")
    print("   - Green 'NEW' badges with pulsing animation")
    print("   - Blue 'UPDATED' badges with pulsing animation")
    print("   - Sidebar notification indicator")
    
    return True

def clean_indicator_demo():
    print("🧹 Cleaning Indicator Demo")
    print("=" * 30)
    
    try:
        demo_user = User.objects.get(mobile='9876543210')
        demo_count = Note.objects.filter(user=demo_user, title__startswith='[DEMO]').count()
        Note.objects.filter(user=demo_user, title__startswith='[DEMO]').delete()
        print(f"✅ Deleted {demo_count} demo indicator notes")
    except User.DoesNotExist:
        print("ℹ️  No demo user found")
    
    print("🧹 Cleanup complete!")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'clean':
        clean_indicator_demo()
    else:
        create_indicator_demo()
        print("\nRun 'python demo_new_indicators.py clean' to remove demo indicator notes")

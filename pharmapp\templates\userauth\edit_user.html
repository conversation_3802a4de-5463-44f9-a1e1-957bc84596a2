{% extends "partials/base.html" %}
{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="text-center">Edit User</h1>
            <p class="text-center text-muted">Update user information and privileges</p>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Edit User: {{ user_to_edit.username }}</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{% url 'userauth:edit_user' user_to_edit.id %}">
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="id_full_name">Full Name</label>
                        {{ form.full_name }}
                        {% if form.full_name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.full_name.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="id_username">Username</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.username.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="id_mobile">Mobile Number</label>
                        {{ form.mobile }}
                        {% if form.mobile.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.mobile.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="id_email">Email (Optional)</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.email.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="id_user_type">User Type</label>
                        {{ form.user_type }}
                        {% if form.user_type.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.user_type.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="form-check mt-4">
                            {{ form.is_active }}
                            <label class="form-check-label" for="id_is_active">
                                Active Account
                            </label>
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.is_active.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12 text-center">
                        <a href="{% url 'userauth:user_list' %}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

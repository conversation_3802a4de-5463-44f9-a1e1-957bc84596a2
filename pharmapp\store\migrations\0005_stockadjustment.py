# Generated by Django 5.1.5 on 2025-02-06 14:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0004_rename_user_stockcheck_created_by_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StockAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjusted_quantity', models.PositiveIntegerField()),
                ('adjusted_at', models.DateTimeField(auto_now_add=True)),
                ('adjusted_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('stock_check_item', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='store.stockcheckitem')),
            ],
        ),
    ]

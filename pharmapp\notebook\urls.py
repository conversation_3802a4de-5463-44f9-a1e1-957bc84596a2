from django.urls import path
from . import views

app_name = 'notebook'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),
    
    # Note management
    path('notes/', views.note_list, name='note_list'),
    path('notes/create/', views.note_create, name='note_create'),
    path('notes/<int:pk>/', views.note_detail, name='note_detail'),
    path('notes/<int:pk>/edit/', views.note_edit, name='note_edit'),
    path('notes/<int:pk>/delete/', views.note_delete, name='note_delete'),
    path('notes/<int:pk>/archive/', views.note_archive, name='note_archive'),
    path('notes/<int:pk>/pin/', views.note_pin, name='note_pin'),
    
    # Archived notes
    path('archived/', views.archived_notes, name='archived_notes'),
    
    # Categories
    path('categories/', views.category_list, name='category_list'),
    path('categories/create/', views.category_create, name='category_create'),

    # API endpoints
    path('api/quick-create/', views.quick_note_create, name='quick_note_create'),
    path('api/search/', views.note_search_api, name='note_search_api'),

    # Tag-based filtering
    path('tags/<str:tag>/', views.notes_by_tag, name='notes_by_tag'),
]

#!/usr/bin/env python
"""
Simple verification script for the User Management System
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.urls import reverse
from userauth.models import USER_PERMISSIONS

def verify_urls():
    """Verify that all URLs are properly configured"""
    print("🔗 Verifying URL Configuration...")

    urls_to_test = [
        'userauth:user_list',
        'userauth:register',
        'userauth:privilege_management_view',
        'userauth:bulk_user_actions',
    ]

    for url_name in urls_to_test:
        try:
            url = reverse(url_name)
            print(f"  ✓ {url_name} -> {url}")
        except Exception as e:
            print(f"  ✗ {url_name} failed: {e}")
            return False

    return True

def verify_permissions():
    """Verify permission system is properly configured"""
    print("\n🛡️ Verifying Permission System...")

    total_permissions = 0
    for role, permissions in USER_PERMISSIONS.items():
        print(f"  ✓ {role}: {len(permissions)} permissions")
        total_permissions += len(permissions)

    print(f"  ✓ Total unique permissions: {len(set().union(*USER_PERMISSIONS.values()))}")
    return True

def verify_models():
    """Verify models are properly configured"""
    print("\n📊 Verifying Models...")

    try:
        from userauth.models import User, Profile
        print("  ✓ User model imported successfully")
        print("  ✓ Profile model imported successfully")

        # Check if models have required methods
        user_methods = ['has_permission', 'get_permissions']
        for method in user_methods:
            if hasattr(User, method):
                print(f"  ✓ User.{method} method exists")
            else:
                print(f"  ✗ User.{method} method missing")
                return False

        profile_methods = ['get_role_permissions', 'has_permission']
        for method in profile_methods:
            if hasattr(Profile, method):
                print(f"  ✓ Profile.{method} method exists")
            else:
                print(f"  ✗ Profile.{method} method missing")
                return False

        return True
    except Exception as e:
        print(f"  ✗ Model verification failed: {e}")
        return False

def verify_forms():
    """Verify forms are properly configured"""
    print("\n📝 Verifying Forms...")

    try:
        from userauth.forms import UserRegistrationForm, UserEditForm, UserSearchForm, PrivilegeManagementForm
        print("  ✓ UserRegistrationForm imported successfully")
        print("  ✓ UserEditForm imported successfully")
        print("  ✓ UserSearchForm imported successfully")
        print("  ✓ PrivilegeManagementForm imported successfully")
        return True
    except Exception as e:
        print(f"  ✗ Form verification failed: {e}")
        return False

def verify_decorators():
    """Verify decorators are properly configured"""
    print("\n🔒 Verifying Decorators...")

    try:
        from userauth.decorators import (
            permission_required, role_required, admin_required,
            manager_or_admin_required, pharmacist_or_above_required,
            staff_required, PermissionMixin
        )
        print("  ✓ permission_required decorator imported")
        print("  ✓ role_required decorator imported")
        print("  ✓ admin_required decorator imported")
        print("  ✓ manager_or_admin_required decorator imported")
        print("  ✓ pharmacist_or_above_required decorator imported")
        print("  ✓ staff_required decorator imported")
        print("  ✓ PermissionMixin class imported")
        return True
    except Exception as e:
        print(f"  ✗ Decorator verification failed: {e}")
        return False

def verify_templates():
    """Verify templates exist"""
    print("\n🎨 Verifying Templates...")

    import os
    template_dir = 'templates/userauth'
    templates_to_check = [
        'user_list.html',
        'user_details.html',
        'privilege_management.html',
        'register.html'
    ]

    for template in templates_to_check:
        template_path = os.path.join(template_dir, template)
        if os.path.exists(template_path):
            print(f"  ✓ {template} exists")
        else:
            print(f"  ✗ {template} missing")
            return False

    # Check 403 template
    if os.path.exists('templates/403.html'):
        print("  ✓ 403.html exists")
    else:
        print("  ✗ 403.html missing")
        return False

    return True

def verify_template_filters():
    """Verify custom template filters"""
    print("\n🏷️ Verifying Template Filters...")

    try:
        from userauth.templatetags.user_filters import replace, format_permission, has_permission, get_user_role
        print("  ✓ replace filter imported")
        print("  ✓ format_permission filter imported")
        print("  ✓ has_permission filter imported")
        print("  ✓ get_user_role filter imported")

        # Test format_permission filter
        test_permission = "manage_users"
        formatted = format_permission(test_permission)
        expected = "Manage Users"
        if formatted == expected:
            print(f"  ✓ format_permission works correctly: '{test_permission}' -> '{formatted}'")
        else:
            print(f"  ✗ format_permission failed: expected '{expected}', got '{formatted}'")
            return False

        # Test replace filter
        test_string = "hello_world"
        replaced = replace(test_string, "_,")
        expected = "hello world"
        if replaced == expected:
            print(f"  ✓ replace filter works correctly: '{test_string}' -> '{replaced}'")
        else:
            print(f"  ✗ replace filter failed: expected '{expected}', got '{replaced}'")
            return False

        return True
    except Exception as e:
        print(f"  ✗ Template filter verification failed: {e}")
        return False

def print_implementation_summary():
    """Print summary of what was implemented"""
    print("\n" + "="*60)
    print("USER MANAGEMENT SYSTEM - IMPLEMENTATION COMPLETE")
    print("="*60)

    print("\n🎯 KEY FEATURES IMPLEMENTED:")
    print("  • Enhanced User and Profile models with additional fields")
    print("  • Comprehensive role-based permission system")
    print("  • User management interface with CRUD operations")
    print("  • Advanced search and filtering capabilities")
    print("  • Bulk user operations (activate, deactivate, delete)")
    print("  • Privilege management interface with role templates")
    print("  • Detailed user information pages")
    print("  • Enhanced registration and editing forms")
    print("  • Security decorators for view protection")
    print("  • Template context processors for permissions")
    print("  • Navigation integration with role-based visibility")
    print("  • Custom 403 error handling")

    print("\n👥 USER ROLES & PERMISSIONS:")
    for role, permissions in USER_PERMISSIONS.items():
        print(f"  • {role}: {len(permissions)} permissions")
        if role == 'Admin':
            print("    - Full system access including user management")
        elif role == 'Manager':
            print("    - Supervisory access excluding user creation/deletion")
        elif role == 'Pharmacist':
            print("    - Full pharmacy operations and inventory management")
        elif role == 'Pharm-Tech':
            print("    - Limited pharmacy operations and basic dispensing")
        elif role == 'Salesperson':
            print("    - Sales operations and customer management")

    print("\n🔗 AVAILABLE ENDPOINTS:")
    print("  • /users/ - User management dashboard")
    print("  • /register/ - User registration form")
    print("  • /users/details/<id>/ - User detail pages")
    print("  • /privilege-management/ - Privilege management interface")
    print("  • /users/bulk-actions/ - Bulk user operations")

    print("\n🛡️ SECURITY FEATURES:")
    print("  • Role-based access control throughout the system")
    print("  • Permission validation at view and template levels")
    print("  • Activity logging for all user management actions")
    print("  • Automatic superuser privilege assignment")
    print("  • Secure form validation and error handling")

    print("\n📱 USER INTERFACE:")
    print("  • Enhanced user list with search, filter, and pagination")
    print("  • Comprehensive user creation and editing forms")
    print("  • Interactive privilege management with role templates")
    print("  • Detailed user profiles with permission overview")
    print("  • Bulk selection and action controls")
    print("  • Integrated navigation with role-based visibility")

    print("\n🚀 NEXT STEPS:")
    print("  1. Navigate to the admin panel to create your first admin user")
    print("  2. Access /users/ to start managing users")
    print("  3. Use /register/ to create new users with specific roles")
    print("  4. Configure user privileges via /privilege-management/")
    print("  5. Monitor user activity through the activity logs")

    print("\n✅ SYSTEM READY FOR USE!")

def main():
    """Main verification function"""
    print("🔍 VERIFYING USER MANAGEMENT SYSTEM IMPLEMENTATION")
    print("="*60)

    all_checks_passed = True

    # Run all verification checks
    checks = [
        verify_urls,
        verify_permissions,
        verify_models,
        verify_forms,
        verify_decorators,
        verify_templates
    ]

    for check in checks:
        if not check():
            all_checks_passed = False

    if all_checks_passed:
        print("\n🎉 ALL VERIFICATIONS PASSED!")
        print_implementation_summary()
    else:
        print("\n❌ SOME VERIFICATIONS FAILED!")
        print("Please check the errors above and fix any issues.")
        return False

    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)

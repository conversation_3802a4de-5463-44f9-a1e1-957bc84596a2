#!/usr/bin/env python
"""
Script to fix user profiles with missing or None user_type values.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from userauth.models import User, Profile


def fix_profiles():
    """Fix profiles with missing or None user_type"""
    print("Fixing User Profiles")
    print("=" * 50)
    
    # Find profiles with None or empty user_type
    problematic_profiles = Profile.objects.filter(user_type__isnull=True) | Profile.objects.filter(user_type='')
    
    print(f"Found {problematic_profiles.count()} profiles with missing user_type")
    
    fixed_count = 0
    for profile in problematic_profiles:
        # Set default user_type based on user status
        if profile.user.is_superuser:
            profile.user_type = 'Admin'
        elif profile.user.is_staff:
            profile.user_type = 'Manager'
        else:
            profile.user_type = 'Salesperson'  # Default for regular users
        
        # Set full_name if missing
        if not profile.full_name:
            profile.full_name = profile.user.username or profile.user.mobile
        
        profile.save()
        fixed_count += 1
        
        print(f"Fixed profile for {profile.user.mobile} ({profile.user.username}) - Set to {profile.user_type}")
    
    # Also check for users without profiles
    users_without_profiles = []
    for user in User.objects.all():
        try:
            profile = user.profile
        except Profile.DoesNotExist:
            users_without_profiles.append(user)
    
    print(f"\nFound {len(users_without_profiles)} users without profiles")
    
    created_count = 0
    for user in users_without_profiles:
        # Determine user_type based on user status
        if user.is_superuser:
            user_type = 'Admin'
        elif user.is_staff:
            user_type = 'Manager'
        else:
            user_type = 'Salesperson'
        
        profile = Profile.objects.create(
            user=user,
            full_name=user.username or user.mobile,
            user_type=user_type
        )
        created_count += 1
        
        print(f"Created profile for {user.mobile} ({user.username}) - Set to {user_type}")
    
    print("\n" + "=" * 50)
    print(f"Fixed {fixed_count} existing profiles")
    print(f"Created {created_count} new profiles")
    print("Profile fix complete!")


if __name__ == "__main__":
    fix_profiles()

{% load static %}
<!DOCTYPE html>
<html>

<head>
    <title>Daily Sales</title>
    <!-- Custom fonts for this template-->
    <link href="{% static 'vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="{% static 'css/sb-admin-2.min.css' %}" rel="stylesheet">
    <!-- <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}"> -->
    <!-- <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script> -->
    <script src="{% static 'js/demo/chart.min.js' %}"></script>
    <style>
        .container {
            margin: 20px auto;
            max-width: 1200px;
            padding: 0 15px;
        }

        .table {
            color: #333;
        }

        .chart-container {
            margin-top: 50px;
            width: 100%;
            height: 400px;
        }

        /* Media Queries */
        @media (max-width: 768px) {
            .container {
                max-width: 100%;
                padding: 0 10px;
            }

            .chart-container {
                height: 300px;
            }

            h1 {
                font-size: 1.5rem;
            }

            table {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 576px) {
            .container {
                padding: 0 5px;
            }

            .chart-container {
                height: 250px;
            }

            h1 {
                font-size: 1.2rem;
                text-align: center;
            }

            table {
                font-size: 0.8rem;
            }

            .btn {
                font-size: 0.8rem;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <a href="{{ request.META.HTTP_REFERER }}" class="btn btn-outline-dark btn-sm">Back</a>
        <div class="col-md-10 offset-md-1">
            {% if daily_sales %}
            <h1 style="text-align: center;">Daily Sales Summary</h1>
            <table class="table table-hover" style="box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <thead class="table-primary">
                    <tr>
                        <th scope="col">Date</th>
                        <th scope="col">Total Sales</th>
                        <th scope="col">Total Cost</th>
                        <th scope="col">Total Profit</th>
                        <th scope="col">Cash</th>
                        <th scope="col">Wallet</th>
                        <th scope="col">Transfer</th>
                    </tr>
                </thead>
                <tbody>
                    {% for day, sale in daily_sales %}
                    <tr>
                        <td>{{ day }}</td>
                        <td>{{ sale.total_sales|floatformat:2 }}</td>
                        <td>{{ sale.total_cost|floatformat:2 }}</td>
                        <td>{{ sale.total_profit|floatformat:2 }}</td>
                        <td>{{ sale.payment_methods.Cash|floatformat:2 }}</td>
                        <td>{{ sale.payment_methods.Wallet|floatformat:2 }}</td>
                        <td>{{ sale.payment_methods.Transfer|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <h4>No Sales for the Day</h4>
            <a href="{{ request.META.HTTP_REFERER }}" class="btn btn-secondary form-control" style="width: 100%;">Go
                Back</a>
            {% endif %}
        </div>

        <div class="col-md-10 offset-md-1 chart-container">
            <canvas id="dailySalesChart"></canvas>
        </div>

        <div class="col-md-10 offset-md-1 chart-container mt-5">
            <h3 style="text-align: center;">Sales by Payment Method</h3>
            <canvas id="paymentMethodChart"></canvas>
        </div>
    </div>

    <script>
        // Daily Sales Chart
        const dailySalesData = {
            labels: [
                {% for day, sale in daily_sales %}
        "{{ day }}",
            {% endfor %}
            ],
        datasets: [{
            label: 'Daily Sales',
            backgroundColor: [
                'rgba(54, 162, 235, 0.7)',
            ],
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1,
            data: [
                {% for day, sale in daily_sales %}
        {{ sale.total_sales|floatformat:2 }},
        {% endfor %}
        ]
            }]
        };

        const dailySalesConfig = {
            type: 'bar',
            data: dailySalesData,
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        };

        const dailySalesChart = new Chart(
            document.getElementById('dailySalesChart'),
            dailySalesConfig
        );

        // Payment Method Chart
        const paymentMethodData = {
            labels: [
                {% for day, sale in daily_sales %}
        "{{ day }}",
            {% endfor %}
            ],
            datasets: [
                {
                    label: 'Cash',
                    backgroundColor: 'rgba(75, 192, 192, 0.7)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1,
                    data: [
                        {% for day, sale in daily_sales %}
                {{ sale.payment_methods.Cash|floatformat:2 }},
                        {% endfor %}
                    ]
                },
                {
                    label: 'Wallet',
                    backgroundColor: 'rgba(153, 102, 255, 0.7)',
                    borderColor: 'rgba(153, 102, 255, 1)',
                    borderWidth: 1,
                    data: [
                        {% for day, sale in daily_sales %}
                {{ sale.payment_methods.Wallet|floatformat:2 }},
                        {% endfor %}
                    ]
                },
                {
                    label: 'Transfer',
                    backgroundColor: 'rgba(255, 159, 64, 0.7)',
                    borderColor: 'rgba(255, 159, 64, 1)',
                    borderWidth: 1,
                    data: [
                        {% for day, sale in daily_sales %}
                {{ sale.payment_methods.Transfer|floatformat:2 }},
                        {% endfor %}
                    ]
                }
            ]
        };

        const paymentMethodConfig = {
            type: 'bar',
            data: paymentMethodData,
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Sales by Payment Method'
                    }
                }
            }
        };

        const paymentMethodChart = new Chart(
            document.getElementById('paymentMethodChart'),
            paymentMethodConfig
        );
    </script>
</body>

</html>
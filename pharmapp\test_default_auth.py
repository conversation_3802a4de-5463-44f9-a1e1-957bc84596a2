#!/usr/bin/env python
"""
Test script to verify authentication works with default backend.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import authenticate
from userauth.models import User


def test_default_authentication():
    """Test authentication with Django's default backend"""
    print("Testing Default Authentication")
    print("=" * 50)
    
    # Get a test user
    try:
        user = User.objects.get(mobile='1234567890')  # testuser
        print(f"Found test user: {user.mobile} ({user.username})")
        print(f"  - Active: {user.is_active}")
        print(f"  - USERNAME_FIELD: {User.USERNAME_FIELD}")
        
        # Test authentication with mobile as username
        # Note: We can't test with actual password for security
        print(f"\nTesting authentication parameters:")
        print(f"  - Mobile field: {user.mobile}")
        print(f"  - Username field: {user.username}")
        print(f"  - User model USERNAME_FIELD: {User.USERNAME_FIELD}")
        
        # Check if user has profile
        if hasattr(user, 'profile') and user.profile:
            print(f"  - Profile: {user.profile.full_name} ({user.profile.user_type})")
        else:
            print("  - Profile: Missing")
        
        print("\n✅ Default authentication should work with mobile number")
        print("   (Django will use mobile as username since USERNAME_FIELD = 'mobile')")
        
    except User.DoesNotExist:
        print("❌ Test user not found")
    
    print("\n" + "=" * 50)
    print("Test complete!")


if __name__ == "__main__":
    test_default_authentication()

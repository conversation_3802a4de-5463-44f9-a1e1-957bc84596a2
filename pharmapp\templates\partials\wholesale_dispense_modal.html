<form method="POST" hx-post="{% url 'wholesale:dispense_wholesale' %}" hx-target="#wholesaleDispenseModal .modal-body">
    {% csrf_token %}
    {{ form.as_p }}
    <button type="submit" class="btn btn-success float-end btn-sm">Search</button>
</form>
<br>
{% if results %}
<h5>Search Results:</h5>
<ul class="list-group">
    {% for item in results %}
    <li class="list-group-item">
        <form method="POST" action="{%  url 'wholesale:add_to_wholesale_cart' item.id %}">
            {% csrf_token %}
            <div>{{ item.name }} {{item.dosage_form}} ({{item.brand}})=> {{ item.stock }} {{item.unit}} Available, at
                {{item.price }}
                each.</div>


            <!-- Quantity Input -->
            <div class="mb-2">
                <label for="quantity-{{ item.id }}">Quantity</label>
                <input type="number" name="quantity" min="0.5" step="0.5" value="1" id="quantity-{{ item.id }}" required>
            </div>

            <div></div>
            <button class="btn btn-primary btn-sm float-right" type="submit">Add to Cart</button>
        </form>

    </li>
    {% endfor %}
</ul>
{% endif %}
{% load static %} {% block content %}
<!DOCTYPE html>
<html>
    <head>
        <title>Monthly Sales</title>
        <!-- Custom fonts for this template-->
        <link
            href="{% static 'vendor/fontawesome-free/css/all.min.css' %}"
            rel="stylesheet"
            type="text/css"
        />
        <link
            href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
            rel="stylesheet"
        />

        <!-- Custom styles for this template-->
        <link href="{% static 'css/sb-admin-2.min.css' %}" rel="stylesheet" />
        <!-- <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}"> -->
        <!-- <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>
    <script src="{% static 'htmx/htmx.min.js' %}"></script> -->
        <script src="{% static 'js/demo/chart.min.js' %}"></script>
        <style>
            .container {
                margin: 20px auto;
                max-width: 1200px;
                padding: 0 15px;
            }

            .table {
                color: #333;
            }

            .chart-container {
                margin-top: 20px;
                width: 100%;
                height: 400px;
            }

            /* Media Queries */
            @media (max-width: 768px) {
                .container {
                    max-width: 100%;
                    padding: 0 10px;
                }

                .chart-container {
                    height: 300px;
                }

                h1 {
                    font-size: 1.5rem;
                }
            }

            @media (max-width: 576px) {
                .container {
                    padding: 0 5px;
                }

                .chart-container {
                    height: 250px;
                }

                h1 {
                    font-size: 1.2rem;
                    text-align: center;
                }

                table {
                    font-size: 0.9rem;
                }

                .btn {
                    font-size: 0.8rem;
                }
            }
        </style>
    </head>

    <body>
        <div class="container">
            <a
                href="{{ request.META.HTTP_REFERER }}"
                class="btn btn-outline-dark btn-sm mb-3"
                >Back</a
            >

            <div class="col-md-8 offset-md-2">
                {% if monthly_sales %}
                <h1 class="text-center">Monthly Sales</h1>
                <table
                    class="table table-hover"
                    style="box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1)"
                >
                    <thead class="table-primary">
                        <tr>
                            <th scope="col">Month</th>
                            <th scope="col">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for month, sale in monthly_sales %}
                        <tr>
                            <td>{{ month }}</td>
                            <td>{{ sale.total_sales|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <h1 class="text-center">Monthly Profit</h1>
                <table
                    class="table table-hover"
                    style="box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1)"
                >
                    <thead class="table-primary">
                        <tr>
                            <th scope="col">Date</th>
                            <th scope="col">Total Sales</th>
                            <th scope="col">Total Cost</th>
                            <th scope="col">Total Profit</th>
                            <th scope="col">Cash</th>
                            <th scope="col">Wallet</th>
                            <th scope="col">Transfer</th>
                            <th scope="col">Expenses</th>
                            <th scope="col">Net Profit</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for month, data in monthly_sales %}
                        <tr>
                            <td>{{ month|date:"F Y" }}</td>
                            <td>{{ data.total_sales|floatformat:2 }}</td>
                            <td>{{ data.total_cost|floatformat:2 }}</td>
                            <td>{{ data.total_profit|floatformat:2 }}</td>
                            <td>{{ data.payment_methods.Cash|floatformat:2 }}</td>
                            <td>{{ data.payment_methods.Wallet|floatformat:2 }}</td>
                            <td>{{ data.payment_methods.Transfer|floatformat:2 }}</td>
                            <td class="text-danger">
                                {{ data.total_expense|floatformat:2 }}
                            </td>
                            <td
                                class="fw-bold {% if data.net_profit < 0 %}text-danger{% else %}text-success{% endif %}"
                            >
                                {{ data.net_profit|floatformat:2 }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <div class="chart-container">
                    <canvas
                        class="col-md-6 offset-md-3"
                        id="monthlySalesChart"
                    ></canvas>
                </div>

                <script>
                    var ctx = document.getElementById('monthlySalesChart').getContext('2d');
                    var monthlySalesChart = new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: [{% for month, sale in monthly_sales %}'{{ month }}', {% endfor %}],
                    datasets: [{
                        label: 'Total Sales',
                        data: [{% for month, sale in monthly_sales %}{{ sale.total_sales|floatformat:2 }}, {% endfor %}],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 206, 86, 0.6)',
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)'
                        ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)'
                            ],
                                borderWidth: 1
                            }]
                        },
                    options: {
                        responsive: true,
                            plugins: {
                            legend: {
                                position: 'top',
                                },
                            title: {
                                display: true,
                                    text: 'Sales Distribution by Month'
                            }
                        }
                    }
                    });
                </script>

                {% else %}
                <h4>No Sales for the Month</h4>
                <a
                    href="{{ request.META.HTTP_REFERER }}"
                    class="btn btn-secondary form-control mt-3"
                    style="width: 100%"
                    >Go Back</a
                >
                {% endif %}
            </div>
        </div>

        <div class="container my-4">
            <h2>Monthly Sales with Expense Deductions</h2>

            <!-- Month Selection Form -->
            <form method="get" class="row g-3 mb-4">
                <div class="col-auto">
                    <label for="month" class="visually-hidden"
                        >Select Month:</label
                    >
                    <input
                        type="month"
                        class="form-control"
                        id="month"
                        name="month"
                        value="{{ selected_month }}"
                    />
                </div>
                <div class="col-auto">
                    <button type="submit" class="btn btn-sm btn-primary mb-3">
                        Filter
                    </button>
                </div>
            </form>

            <!-- Sales Table -->
            <table class="table table-hover">
                <thead class="table-primary">
                    <tr>
                        <th>Month</th>
                        <th>Total Sales</th>
                        <th>Total Cost</th>
                        <th>Total Profit</th>
                        <th>Cash</th>
                        <th>Wallet</th>
                        <th>Transfer</th>
                        <th>Total Expenses</th>
                        <th>Net Profit</th>
                    </tr>
                </thead>
                <tbody>
                    {% for month, data in monthly_sales %}
                    <tr>
                        <td>{{ month|date:"F Y" }}</td>
                        <td>{{ data.total_sales|floatformat:2 }}</td>
                        <td>{{ data.total_cost|floatformat:2 }}</td>
                        <td>{{ data.total_profit|floatformat:2 }}</td>
                        <td>{{ data.payment_methods.Cash|floatformat:2 }}</td>
                        <td>{{ data.payment_methods.Wallet|floatformat:2 }}</td>
                        <td>{{ data.payment_methods.Transfer|floatformat:2 }}</td>
                        <td class="text-danger">
                            {{ data.total_expense|floatformat:2 }}
                        </td>
                        <td
                            class="{% if data.net_profit < 0 %}text-danger{% else %}text-success{% endif %}"
                        >
                            {{ data.net_profit|floatformat:2 }}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center">
                            No sales data found for the selected month.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Payment Method Chart -->
            <div class="chart-container mt-5">
                <h3 class="text-center">Monthly Sales by Payment Method</h3>
                <canvas id="paymentMethodChart"></canvas>
            </div>

            <script>
                // Payment Method Chart
                const paymentMethodData = {
                    labels: [
                        {% for month, data in monthly_sales %}
                        "{{ month|date:'F Y' }}",
                        {% endfor %}
                    ],
                    datasets: [
                        {
                            label: 'Cash',
                            backgroundColor: 'rgba(75, 192, 192, 0.7)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1,
                            data: [
                                {% for month, data in monthly_sales %}
                                {{ data.payment_methods.Cash|floatformat:2 }},
                                {% endfor %}
                            ]
                        },
                        {
                            label: 'Wallet',
                            backgroundColor: 'rgba(153, 102, 255, 0.7)',
                            borderColor: 'rgba(153, 102, 255, 1)',
                            borderWidth: 1,
                            data: [
                                {% for month, data in monthly_sales %}
                                {{ data.payment_methods.Wallet|floatformat:2 }},
                                {% endfor %}
                            ]
                        },
                        {
                            label: 'Transfer',
                            backgroundColor: 'rgba(255, 159, 64, 0.7)',
                            borderColor: 'rgba(255, 159, 64, 1)',
                            borderWidth: 1,
                            data: [
                                {% for month, data in monthly_sales %}
                                {{ data.payment_methods.Transfer|floatformat:2 }},
                                {% endfor %}
                            ]
                        }
                    ]
                };

                const paymentMethodConfig = {
                    type: 'bar',
                    data: paymentMethodData,
                    options: {
                        responsive: true,
                        scales: {
                            x: {
                                stacked: false,
                            },
                            y: {
                                stacked: false,
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: 'Sales by Payment Method'
                            }
                        }
                    }
                };

                const paymentMethodChart = new Chart(
                    document.getElementById('paymentMethodChart'),
                    paymentMethodConfig
                );
            </script>
        </div>
    </body>
</html>
{% endblock %}

<style>
    .modal-header {
        background-color: #f8f9fa;
        padding: 10px 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .modal-header h3 {
        font-size: 17px;
        margin: 0;
    }

    .modal-body {
        padding: 50px;
    }

    .modal-footer {
        padding: 10px 15px;
        border-top: 1px solid #dee2e6;
    }

    .modal-body form {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .modal-body form .form-group {
        display: flex;
        align-items: center;
    }

    .modal-body form label {
        flex: 1;
        margin-bottom: 3px;
        font-weight: 700;
    }

    .modal-body form .form-control {
        flex: 2;
        padding: 5px 10px;
    }

    .modal-body form button {
        align-self: flex-end;
        margin-top: 10px;
    }

    .btn-sm {
        padding: 3px 10px;
    }

    .modal-footer .btn {
        margin-right: 5px;
    }

    .modal-footer .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }
</style>


<div class="modal-header">
    <h3 style="text-align: center;">RETURN WHOLESALE ITEM</h3>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">x</button>
</div>
<div class="modal-body">
    <form action="{% url 'wholesale:return_wholesale_item' item.id %}" method="post">
        {% csrf_token %}
        <div class="form-group">
            <label for="name">Item Name:</label>
            <input type="text" name="name" class="form-control mb-3" value="{{ item.name }}" readonly>
        </div>
        <div class="form-group">
            <label for="price">Selling Price:</label>
            <input type="number" name="price" class="form-control mb-3" value="{{ item.price }}" readonly>
        </div>
        <div class="form-group">
            <label for="return_item_quantity">Return Quantity:</label>
            <input type="number" name="return_item_quantity" class="form-control mb-3" required>
        </div>
        <div class="form-group">
            <label for="return_reason">Reason for Return:</label>
            <textarea name="return_reason" class="form-control mb-3" required></textarea>
        </div>
        <button type="submit" class="btn btn-success btn-sm">Process Return</button>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
</div>

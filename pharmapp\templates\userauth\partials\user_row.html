<tr id="user-row-{{ user.id }}">
    <td>{{ user.username }}</td>
    <td>{{ user.profile.full_name }}</td>
    <td>{{ user.mobile }}</td>
    <td>{{ user.email|default:"N/A" }}</td>
    <td>
        <span class="badge badge-primary">{{ user.profile.user_type }}</span>
    </td>
    <td>
        {% if user.is_active %}
            <span class="badge badge-success">Active</span>
        {% else %}
            <span class="badge badge-danger">Inactive</span>
        {% endif %}
    </td>
    <td>
        <div class="btn-group" role="group">
            <a href="{% url 'userauth:edit_user' user.id %}" class="btn btn-sm btn-info"
               data-toggle="modal" data-target="#editUserModal"
               hx-get="{% url 'userauth:edit_user' user.id %}"
               hx-target="#editUserModal .modal-content">
                <i class="fas fa-edit"></i> Edit
            </a>
            
            {% if user != request.user %}
                <a href="{% url 'userauth:toggle_user_status' user.id %}" class="btn btn-sm {% if user.is_active %}btn-warning{% else %}btn-success{% endif %}"
                   hx-get="{% url 'userauth:toggle_user_status' user.id %}"
                   hx-target="#user-row-{{ user.id }}"
                   hx-swap="outerHTML">
                    {% if user.is_active %}
                        <i class="fas fa-ban"></i> Deactivate
                    {% else %}
                        <i class="fas fa-check"></i> Activate
                    {% endif %}
                </a>
                
                <a href="{% url 'userauth:delete_user' user.id %}" class="btn btn-sm btn-danger"
                   onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.');">
                    <i class="fas fa-trash"></i> Delete
                </a>
            {% endif %}
        </div>
    </td>
</tr>

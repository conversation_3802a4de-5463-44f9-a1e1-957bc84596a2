#!/usr/bin/env python
"""
Test script to verify the authentication backend is working.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import authenticate
from userauth.models import User


def test_authentication_backend():
    """Test the custom authentication backend"""
    print("Testing Authentication Backend")
    print("=" * 50)
    
    # Get a test user (we'll use one that exists)
    test_users = [
        ('1234567890', 'testuser'),  # mobile, username
        ('9876543210', 'profiletest'),
        ('1110910', 'test_admin'),
    ]
    
    for mobile, username in test_users:
        try:
            user = User.objects.get(mobile=mobile)
            print(f"\nTesting user: {mobile} ({username})")
            print(f"  - User exists: ✓")
            print(f"  - Active: {user.is_active}")
            print(f"  - Has profile: {hasattr(user, 'profile') and user.profile is not None}")
            
            if hasattr(user, 'profile') and user.profile:
                print(f"  - User type: {user.profile.user_type}")
            
            # Test authentication with mobile (without password for security)
            print(f"  - Authentication backend ready for mobile: {mobile}")
            
        except User.DoesNotExist:
            print(f"\nUser {mobile} not found")
    
    print("\n" + "=" * 50)
    print("Authentication backend test complete!")
    print("\nTo test actual login:")
    print("1. Start the server: python manage.py runserver")
    print("2. Go to http://localhost:8000")
    print("3. Try logging in with any of the test users")
    print("   (You'll need to know their passwords)")


if __name__ == "__main__":
    test_authentication_backend()

{% load static %}
{% load humanize %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt Details - {{ receipt.receipt_id }}</title>
    <link href="{% static 'vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/sb-admin-2.min.css' %}" rel="stylesheet">
    <link href="{% static 'css/receipt_print.css' %}" rel="stylesheet">
    <script src="{% static 'htmx/htmx.min.js' %}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
</head>

<body>
    <div class="receipt-container" id="receipt">
        <header>
            <div class="receipt-head" style="text-align: center;">
                <h3>Nazz Pharmacy</h3>
                <p>No. 123 FTH Jibia Bypass, Katsina</p>
                <p>Tel: +234-XXX-XXX-XXXX</p>
            </div>
        </header>

        <p><strong>Receipt ID:</strong> {{ receipt.receipt_id }}</p>
        <p><strong>Receipt Type:</strong> <strong>Wholesale</strong></p>
        <p><strong>Customer Name:</strong>
            {% if receipt.wholesale_customer %}
            {{ receipt.wholesale_customer.name|upper }}
            {% elif receipt.buyer_name %}
            {{ receipt.buyer_name|upper }}
            {% else %}
            WALK-IN CUSTOMER
            {% endif %}
        </p>
        <p><strong>Customer Address:</strong>
            {% if receipt.buyer_address %}
            {{ receipt.buyer_address|upper }}
            {% else %}
            Not Provided
            {% endif %}
        </p>
        <p><strong>Date:</strong> {{ receipt.date|date:"F j, Y" }}</p>
        <p><strong>Sales Person:</strong> {{ user.username }}</p>

        {% if receipt.payment_method == 'Split' %}
        <div style="border: 1px solid #ddd; padding: 12px; margin: 10px 0; border-radius: 5px; background-color: #f8f9fa;">
            <p style="font-weight: bold; margin-bottom: 8px;">Payment Method: Split Payment</p>
            <div style="margin-left: 10px;">
                {% if wholesale_receipt_payments %}
                    {% for payment in wholesale_receipt_payments %}
                    <p style="margin: 4px 0; font-weight: 500;">
                        <span style="display: inline-block; width: 80px;">{{ payment.payment_method }}:</span>
                        <span style="color: #0066cc; font-weight: bold;">₦{{ payment.amount|floatformat:2|intcomma }}</span>
                    </p>
                    {% endfor %}
                {% elif split_payment_details %}
                    <p style="margin: 4px 0; font-weight: 500;">
                        <span style="display: inline-block; width: 80px;">{{ split_payment_details.payment_method_1 }}:</span>
                        <span style="color: #0066cc; font-weight: bold;">₦{{ split_payment_details.payment_amount_1|floatformat:2|intcomma }}</span>
                    </p>
                    <p style="margin: 4px 0; font-weight: 500;">
                        <span style="display: inline-block; width: 80px;">{{ split_payment_details.payment_method_2 }}:</span>
                        <span style="color: #0066cc; font-weight: bold;">₦{{ split_payment_details.payment_amount_2|floatformat:2|intcomma }}</span>
                    </p>
                {% else %}
                    <p style="color: #dc3545; font-style: italic;">Split payment details not available</p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <p><strong>Payment Method:</strong> {{ receipt.payment_method }}</p>
        <p><strong>Payment Status:</strong> {{ receipt.status }}</p>

        <div class="table-responsive mt-3">
            <table class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Item Name</th>
                        <th class="dosage-form-column">Dosage Form</th>
                        <th class="brand-column">Brand</th>
                        <th>Unit</th>
                        <th>Qty</th>
                        <th>Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sales_item in sales_items %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ sales_item.item.name|title }}</td>
                        <td class="dosage-form-column">{{ sales_item.item.dosage_form|title }}</td>
                        <td class="brand-column">{{ sales_item.item.brand|title }}</td>
                        <td>{{ sales_item.item.unit }}</td>
                        <td>{{ sales_item.quantity|intcomma }}</td>
                        <td>₦{{ sales_item.price|intcomma }}</td>
                        <td>₦{{ sales_item.subtotal|floatformat:2|intcomma }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <p style="text-align: end;"><strong>Total Amount: ₦{{ receipt.total_amount|floatformat:2|intcomma }}</strong></p>

        {% if receipt.has_returns %}
        <div class="returns-section mt-4">
            <h4>Returns Information</h4>
            <pre>{{ receipt.return_notes }}</pre>
        </div>
        {% endif %}

        <footer>
            <p>___Thank you for your patronage___</p>
            <p>This is a computer-generated wholesale receipt</p>
            <small>{{ receipt.date|date:"F j, Y H:i" }}</small>
        </footer>
    </div>

    <div class="action-buttons no-print">
        <button class="btn btn-primary btn-sm" onclick="printReceipt('thermal')">
            <i class="fas fa-print"></i> Thermal Print
        </button>
        <button class="btn btn-secondary btn-sm" onclick="printReceipt('a4')">
            <i class="fas fa-print"></i> A4 Print
        </button>
        <button onclick="downloadPDF()" class="btn btn-sm btn-success">
            <i class="fas fa-download"></i> Download PDF
        </button>
        <button class="btn btn-info btn-sm" onclick="window.location.reload()">
            <i class="fas fa-redo"></i> Reset
        </button>
        <a href="{{ request.META.HTTP_REFERER }}" class="btn btn-outline-dark btn-sm">Back</a>
    </div>

    <script>
    function printReceipt(format) {
        const originalTitle = document.title;
        const receipt = document.getElementById('receipt');
        const originalWidth = receipt.style.width;

        // Store original display values of dosage form columns
        const dosageFormCells = document.querySelectorAll('.dosage-form-column');
        const originalDisplayValues = Array.from(dosageFormCells).map(cell => cell.style.display);

        if (format === 'thermal') {
            receipt.style.width = '80mm'; // Increased from 72mm
            receipt.style.margin = '0 auto';
            receipt.style.padding = '2mm';
            document.title = 'Thermal_' + originalTitle;

            // Add thermal-specific styles
            receipt.classList.add('thermal-print');
            receipt.classList.add('hide-dosage');
            receipt.style.fontSize = '9px';
            receipt.style.lineHeight = '1.1';

            // Hide dosage form columns
            dosageFormCells.forEach(cell => {
                cell.style.display = 'none';
            });

            // Ensure table fits within thermal width
            const table = receipt.querySelector('table');
            if (table) {
                table.style.width = '76mm'; // Increased from 68mm
                table.style.fontSize = '8px';
                table.style.tableLayout = 'fixed';
            }
        } else {
            // A4 format settings
            receipt.style.width = '210mm';
            document.title = 'A4_' + originalTitle;
            receipt.classList.remove('thermal-print');
            receipt.classList.remove('hide-dosage');

            // Show dosage form columns
            dosageFormCells.forEach((cell, index) => {
                cell.style.display = originalDisplayValues[index];
            });

            // Reset table styles
            const table = receipt.querySelector('table');
            if (table) {
                table.style.width = '100%';
                table.style.fontSize = '';
                table.style.tableLayout = '';
            }
        }

        window.print();

        // Restore original styles after printing
        setTimeout(() => {
            receipt.style.width = originalWidth;
            receipt.style.margin = '';
            receipt.style.padding = '';
            receipt.classList.remove('thermal-print');
            receipt.classList.remove('hide-dosage');
            document.title = originalTitle;

            // Restore dosage form columns
            dosageFormCells.forEach((cell, index) => {
                cell.style.display = originalDisplayValues[index];
            });

            // Reset all modified styles
            const table = receipt.querySelector('table');
            if (table) {
                table.style.width = '';
                table.style.fontSize = '';
                table.style.tableLayout = '';
            }
        }, 100);
    }

    function downloadPDF() {
        const receipt = document.getElementById('receipt');
        const options = {
            margin: 10,
            filename: 'Receipt_{{ receipt.receipt_id }}.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        };

        html2pdf().from(receipt).set(options).save();
    }

    window.onafterprint = function() {
        document.title = 'Receipt Details - {{ receipt.receipt_id }}';
    };
    </script>
</body>

</html>

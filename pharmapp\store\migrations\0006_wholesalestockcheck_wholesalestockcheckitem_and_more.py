# Generated by Django 5.1.5 on 2025-02-07 10:25

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0005_stockadjustment'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WholesaleStockCheck',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed')], default='pending', max_length=10)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleStockCheckItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expected_quantity', models.PositiveIntegerField()),
                ('actual_quantity', models.PositiveIntegerField()),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.wholesaleitem')),
                ('stock_check', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.wholesalestockcheck')),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleStockAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjusted_quantity', models.PositiveIntegerField()),
                ('adjusted_at', models.DateTimeField(auto_now_add=True)),
                ('adjusted_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('stock_check_item', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='store.wholesalestockcheckitem')),
            ],
        ),
    ]

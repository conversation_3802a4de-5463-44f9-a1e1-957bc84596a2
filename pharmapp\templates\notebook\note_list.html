{% extends 'notebook/base_notebook.html' %}
{% load static %}

{% block notebook_title %}
    {% if is_archived_view %}Archived Notes{% else %}My Notes{% endif %}
{% endblock %}

{% block notebook_subtitle %}
    {% if is_archived_view %}
        View and manage your archived notes
    {% else %}
        Manage and organize your notes
    {% endif %}
{% endblock %}

{% block notebook_content %}
<!-- Search and Filter Form -->
{% if not is_archived_view %}
<div class="search-form">
    <form method="get" class="row g-3">
        <div class="col-md-4">
            {{ search_form.query }}
        </div>
        <div class="col-md-2">
            {{ search_form.category }}
        </div>
        <div class="col-md-2">
            {{ search_form.priority }}
        </div>
        <div class="col-md-2">
            <div class="form-check">
                {{ search_form.is_pinned }}
                <label class="form-check-label" for="{{ search_form.is_pinned.id_for_label }}">
                    Pinned only
                </label>
            </div>
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> Search
            </button>
            <a href="{% url 'notebook:note_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-times"></i>
            </a>
        </div>
    </form>
</div>
{% endif %}

<!-- Notes Header -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <h5 class="mb-0">
        {% if is_archived_view %}
            Archived Notes ({{ total_notes }})
        {% else %}
            Notes ({{ total_notes }})
        {% endif %}
    </h5>
    {% if not is_archived_view %}
        <a href="{% url 'notebook:note_create' %}" class="btn btn-notebook">
            <i class="fas fa-plus"></i> New Note
        </a>
    {% endif %}
</div>

<!-- Notes Grid -->
{% if page_obj %}
    <div class="row">
        {% for note in page_obj %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="note-card card h-100 {% if note.is_pinned %}pinned{% endif %} {% if note.priority == 'high' %}high-priority{% elif note.priority == 'urgent' %}urgent-priority{% endif %}">
                    <div class="card-body">
                        <!-- Note Header -->
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">
                                <a href="{% url 'notebook:note_detail' note.pk %}" class="text-decoration-none">
                                    {{ note.title|truncatechars:40 }}
                                </a>
                                {% if note.is_pinned %}
                                    <i class="fas fa-thumbtack text-warning ms-1"></i>
                                {% endif %}
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="{% url 'notebook:note_detail' note.pk %}">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'notebook:note_edit' note.pk %}">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'notebook:note_pin' note.pk %}">
                                            <i class="fas fa-thumbtack"></i> 
                                            {% if note.is_pinned %}Unpin{% else %}Pin{% endif %}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'notebook:note_archive' note.pk %}">
                                            <i class="fas fa-archive"></i> 
                                            {% if note.is_archived %}Unarchive{% else %}Archive{% endif %}
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="{% url 'notebook:note_delete' note.pk %}">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Priority Badge -->
                        <div class="mb-2">
                            <span class="badge {{ note.get_priority_badge_class }} priority-badge">
                                {{ note.get_priority_display }}
                            </span>
                            {% if note.category %}
                                <span class="badge badge-light">
                                    <span class="category-color" style="background-color: {{ note.category.color }};"></span>
                                    {{ note.category.name }}
                                </span>
                            {% endif %}
                        </div>

                        <!-- Note Content -->
                        <p class="card-text note-content">
                            {{ note.content|truncatechars:150 }}
                        </p>

                        <!-- Tags -->
                        {% if note.tags %}
                            <div class="note-tags">
                                {% for tag in note.get_tags_list %}
                                    <span class="note-tag">{{ tag }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Footer -->
                        <div class="card-footer bg-transparent border-0 p-0 mt-3">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> {{ note.updated_at|timesince }} ago
                                {% if note.reminder_date %}
                                    <br>
                                    <i class="fas fa-bell {% if note.is_overdue %}text-danger{% else %}text-info{% endif %}"></i>
                                    {% if note.is_overdue %}
                                        Overdue: {{ note.reminder_date|timesince }} ago
                                    {% else %}
                                        Reminder: {{ note.reminder_date|timeuntil }}
                                    {% endif %}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <nav aria-label="Notes pagination">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.is_pinned %}&is_pinned=on{% endif %}">
                            &laquo; First
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.is_pinned %}&is_pinned=on{% endif %}">
                            Previous
                        </a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.is_pinned %}&is_pinned=on{% endif %}">
                            Next
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.query %}&query={{ request.GET.query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.is_pinned %}&is_pinned=on{% endif %}">
                            Last &raquo;
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}

{% else %}
    <!-- Empty State -->
    <div class="text-center py-5">
        <i class="fas fa-sticky-note fa-5x text-muted mb-4"></i>
        <h4 class="text-muted">
            {% if is_archived_view %}
                No archived notes found
            {% else %}
                No notes found
            {% endif %}
        </h4>
        <p class="text-muted">
            {% if is_archived_view %}
                You haven't archived any notes yet.
            {% else %}
                Start organizing your thoughts by creating your first note.
            {% endif %}
        </p>
        {% if not is_archived_view %}
            <a href="{% url 'notebook:note_create' %}" class="btn btn-notebook btn-lg">
                <i class="fas fa-plus"></i> Create Your First Note
            </a>
        {% endif %}
    </div>
{% endif %}
{% endblock %}

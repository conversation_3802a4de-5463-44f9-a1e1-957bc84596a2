# Generated by Django 5.1.7 on 2025-03-09 10:14

import datetime
import django.db.models.deletion
import shortuuid.django_fields
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0001_initial'),
        ('store', '0033_remove_salesitem_brand_remove_salesitem_dosage_form_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='receipt',
            options={},
        ),
        migrations.RemoveConstraint(
            model_name='salesitem',
            name='quantity_not_negative',
        ),
        migrations.RemoveField(
            model_name='receipt',
            name='has_returns',
        ),
        migrations.RemoveField(
            model_name='receipt',
            name='last_modified',
        ),
        migrations.RemoveField(
            model_name='receipt',
            name='return_notes',
        ),
        migrations.AddField(
            model_name='salesitem',
            name='brand',
            field=models.CharField(blank=True, default='None', max_length=225, null=True),
        ),
        migrations.AddField(
            model_name='salesitem',
            name='dosage_form',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.formulation'),
        ),
        migrations.AddField(
            model_name='salesitem',
            name='unit',
            field=models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], default='unit', max_length=10),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='buyer_address',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.customer'),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='date',
            field=models.DateTimeField(default=datetime.datetime.now),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='payment_method',
            field=models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer')], default='Cash', max_length=20),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='receipt_id',
            field=shortuuid.django_fields.ShortUUIDField(alphabet='1234567890', length=5, max_length=50, prefix='', unique=True),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='sales',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='receipts', to='store.sales'),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='status',
            field=models.CharField(choices=[('Paid', 'Paid'), ('Unpaid', 'Unpaid')], default='Unpaid', max_length=20),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.0'), max_digits=10),
        ),
        migrations.AlterField(
            model_name='salesitem',
            name='quantity',
            field=models.IntegerField(),
        ),
        migrations.AlterField(
            model_name='salesitem',
            name='sales',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_items', to='store.sales'),
        ),
    ]

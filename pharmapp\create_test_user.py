#!/usr/bin/env python
"""
Create a test user with known credentials for testing authentication.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import authenticate
from userauth.models import User, Profile


def create_test_user():
    """Create a test user with known credentials"""
    print("Creating Test User for Authentication Testing")
    print("=" * 50)
    
    # Test credentials
    mobile = "9999999998"
    username = "testlogin"
    password = "testpass123"
    
    # Check if user already exists
    if User.objects.filter(mobile=mobile).exists():
        print(f"User with mobile {mobile} already exists. Deleting...")
        User.objects.filter(mobile=mobile).delete()
    
    # Create new user
    user = User.objects.create_user(
        username=username,
        mobile=mobile,
        password=password
    )
    
    # Get or update the profile (created automatically by signal)
    profile = user.profile
    profile.full_name = "Test Login User"
    profile.user_type = "Pharmacist"
    profile.save()
    
    print(f"✅ Created user:")
    print(f"   Mobile: {mobile}")
    print(f"   Username: {username}")
    print(f"   Password: {password}")
    print(f"   Profile: {profile.full_name} ({profile.user_type})")
    
    # Test authentication immediately
    print(f"\n🔍 Testing authentication...")
    
    # Test 1: Using mobile as username
    auth_user = authenticate(username=mobile, password=password)
    if auth_user:
        print(f"✅ Authentication with mobile as username: SUCCESS")
    else:
        print(f"❌ Authentication with mobile as username: FAILED")
    
    # Test 2: Using mobile parameter (if custom backend was working)
    auth_user2 = authenticate(mobile=mobile, password=password)
    if auth_user2:
        print(f"✅ Authentication with mobile parameter: SUCCESS")
    else:
        print(f"❌ Authentication with mobile parameter: FAILED")
    
    # Test 3: Manual password check
    if user.check_password(password):
        print(f"✅ Manual password check: SUCCESS")
    else:
        print(f"❌ Manual password check: FAILED")
    
    print(f"\n📝 Test credentials for login:")
    print(f"   Mobile: {mobile}")
    print(f"   Password: {password}")
    
    print(f"\n🌐 Now test at: http://127.0.0.1:8000")


if __name__ == "__main__":
    create_test_user()

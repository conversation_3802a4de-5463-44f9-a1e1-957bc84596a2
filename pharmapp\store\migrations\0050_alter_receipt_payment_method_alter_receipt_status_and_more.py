# Generated by Django 5.1.7 on 2025-04-23 21:10

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0049_alter_cart_unit_alter_dispensinglog_unit_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='receipt',
            name='payment_method',
            field=models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer'), ('Split', 'Split Payment')], default='Cash', max_length=20),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='status',
            field=models.CharField(choices=[('Paid', 'Paid'), ('Partially Paid', 'Partially Paid'), ('Unpaid', 'Unpaid')], default='Unpaid', max_length=20),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='payment_method',
            field=models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer'), ('Split', 'Split Payment')], default='Cash', max_length=20),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='status',
            field=models.CharField(choices=[('Paid', 'Paid'), ('Partially Paid', 'Partially Paid'), ('Unpaid', 'Unpaid')], default='Unpaid', max_length=20),
        ),
        migrations.CreateModel(
            name='ReceiptPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer')], max_length=20)),
                ('status', models.CharField(choices=[('Paid', 'Paid'), ('Unpaid', 'Unpaid')], default='Paid', max_length=20)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('receipt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='receipt_payments', to='store.receipt')),
            ],
            options={
                'verbose_name': 'Receipt Payment',
                'verbose_name_plural': 'Receipt Payments',
            },
        ),
        migrations.CreateModel(
            name='WholesaleReceiptPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer')], max_length=20)),
                ('status', models.CharField(choices=[('Paid', 'Paid'), ('Unpaid', 'Unpaid')], default='Paid', max_length=20)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('receipt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wholesale_receipt_payments', to='store.wholesalereceipt')),
            ],
            options={
                'verbose_name': 'Wholesale Receipt Payment',
                'verbose_name_plural': 'Wholesale Receipt Payments',
            },
        ),
    ]

# Generated by Django 5.1.7 on 2025-05-27 16:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('userauth', '0004_alter_activitylog_options_activitylog_action_type_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='profile',
            options={'ordering': ['-created_at']},
        ),
        migrations.AddField(
            model_name='profile',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='department',
            field=models.CharField(blank=True, help_text='Department or section', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='employee_id',
            field=models.CharField(blank=True, help_text='Employee ID number', max_length=50, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='hire_date',
            field=models.DateField(blank=True, help_text='Date of employment', null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='last_login_ip',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
    ]

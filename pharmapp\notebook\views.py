from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.utils import timezone
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Note, NoteCategory, NoteShare
from .forms import NoteForm, NoteCategoryForm, NoteSearchForm, NoteShareForm


@login_required
def note_list(request):
    """Display list of user's notes with search and filter functionality"""
    notes = Note.objects.filter(user=request.user, is_archived=False)
    search_form = NoteSearchForm(request.GET)
    
    # Apply search filters
    if search_form.is_valid():
        query = search_form.cleaned_data.get('query')
        category = search_form.cleaned_data.get('category')
        priority = search_form.cleaned_data.get('priority')
        is_pinned = search_form.cleaned_data.get('is_pinned')
        
        if query:
            notes = notes.filter(
                Q(title__icontains=query) |
                Q(content__icontains=query) |
                Q(tags__icontains=query)
            )
        
        if category:
            notes = notes.filter(category=category)
        
        if priority:
            notes = notes.filter(priority=priority)
        
        if is_pinned:
            notes = notes.filter(is_pinned=True)
    
    # Pagination
    paginator = Paginator(notes, 12)  # Show 12 notes per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get categories for the filter dropdown
    categories = NoteCategory.objects.all()
    
    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'categories': categories,
        'total_notes': notes.count(),
    }
    
    return render(request, 'notebook/note_list.html', context)


@login_required
def note_detail(request, pk):
    """Display detailed view of a single note"""
    note = get_object_or_404(Note, pk=pk)
    
    # Check if user has permission to view this note
    if note.user != request.user:
        # Check if note is shared with current user
        if not NoteShare.objects.filter(note=note, shared_with=request.user).exists():
            messages.error(request, "You don't have permission to view this note.")
            return redirect('notebook:note_list')
    
    context = {
        'note': note,
        'can_edit': note.user == request.user or NoteShare.objects.filter(
            note=note, shared_with=request.user, can_edit=True
        ).exists(),
    }
    
    return render(request, 'notebook/note_detail.html', context)


@login_required
def note_create(request):
    """Create a new note"""
    if request.method == 'POST':
        form = NoteForm(request.POST)
        if form.is_valid():
            note = form.save(commit=False)
            note.user = request.user
            note.save()
            messages.success(request, f'Note "{note.title}" created successfully!')
            return redirect('notebook:note_detail', pk=note.pk)
    else:
        form = NoteForm()
    
    context = {
        'form': form,
        'title': 'Create New Note',
        'submit_text': 'Create Note',
    }
    
    return render(request, 'notebook/note_form.html', context)


@login_required
def note_edit(request, pk):
    """Edit an existing note"""
    note = get_object_or_404(Note, pk=pk)
    
    # Check if user has permission to edit this note
    can_edit = note.user == request.user or NoteShare.objects.filter(
        note=note, shared_with=request.user, can_edit=True
    ).exists()
    
    if not can_edit:
        messages.error(request, "You don't have permission to edit this note.")
        return redirect('notebook:note_detail', pk=note.pk)
    
    if request.method == 'POST':
        form = NoteForm(request.POST, instance=note)
        if form.is_valid():
            form.save()
            messages.success(request, f'Note "{note.title}" updated successfully!')
            return redirect('notebook:note_detail', pk=note.pk)
    else:
        form = NoteForm(instance=note)
    
    context = {
        'form': form,
        'note': note,
        'title': f'Edit Note: {note.title}',
        'submit_text': 'Update Note',
    }
    
    return render(request, 'notebook/note_form.html', context)


@login_required
def note_delete(request, pk):
    """Delete a note"""
    note = get_object_or_404(Note, pk=pk, user=request.user)
    
    if request.method == 'POST':
        title = note.title
        note.delete()
        messages.success(request, f'Note "{title}" deleted successfully!')
        return redirect('notebook:note_list')
    
    context = {
        'note': note,
    }
    
    return render(request, 'notebook/note_confirm_delete.html', context)


@login_required
def note_archive(request, pk):
    """Archive/unarchive a note"""
    note = get_object_or_404(Note, pk=pk, user=request.user)
    
    note.is_archived = not note.is_archived
    note.save()
    
    action = "archived" if note.is_archived else "unarchived"
    messages.success(request, f'Note "{note.title}" {action} successfully!')
    
    return redirect('notebook:note_list')


@login_required
def note_pin(request, pk):
    """Pin/unpin a note"""
    note = get_object_or_404(Note, pk=pk, user=request.user)
    
    note.is_pinned = not note.is_pinned
    note.save()
    
    action = "pinned" if note.is_pinned else "unpinned"
    messages.success(request, f'Note "{note.title}" {action} successfully!')
    
    return redirect('notebook:note_list')


@login_required
def archived_notes(request):
    """Display archived notes"""
    notes = Note.objects.filter(user=request.user, is_archived=True)
    
    # Pagination
    paginator = Paginator(notes, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'total_notes': notes.count(),
        'is_archived_view': True,
    }
    
    return render(request, 'notebook/note_list.html', context)


@login_required
def category_list(request):
    """Display list of note categories"""
    categories = NoteCategory.objects.all()
    
    context = {
        'categories': categories,
    }
    
    return render(request, 'notebook/category_list.html', context)


@login_required
def category_create(request):
    """Create a new note category"""
    if request.method == 'POST':
        form = NoteCategoryForm(request.POST)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'Category "{category.name}" created successfully!')
            return redirect('notebook:category_list')
    else:
        form = NoteCategoryForm()
    
    context = {
        'form': form,
        'title': 'Create New Category',
        'submit_text': 'Create Category',
    }
    
    return render(request, 'notebook/category_form.html', context)


@login_required
def dashboard(request):
    """Notebook dashboard with overview and quick actions"""
    user_notes = Note.objects.filter(user=request.user)
    
    # Statistics
    total_notes = user_notes.count()
    pinned_notes = user_notes.filter(is_pinned=True).count()
    archived_notes = user_notes.filter(is_archived=True).count()
    recent_notes = user_notes.filter(is_archived=False)[:5]
    
    # Upcoming reminders
    upcoming_reminders = user_notes.filter(
        reminder_date__gte=timezone.now(),
        is_archived=False
    ).order_by('reminder_date')[:5]
    
    # Overdue reminders
    overdue_reminders = user_notes.filter(
        reminder_date__lt=timezone.now(),
        is_archived=False
    ).order_by('reminder_date')[:5]
    
    context = {
        'total_notes': total_notes,
        'pinned_notes': pinned_notes,
        'archived_notes': archived_notes,
        'recent_notes': recent_notes,
        'upcoming_reminders': upcoming_reminders,
        'overdue_reminders': overdue_reminders,
    }
    
    return render(request, 'notebook/dashboard.html', context)

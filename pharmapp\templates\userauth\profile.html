{% extends "partials/base.html" %}
{% block content %}

<style>
    /* General <PERSON> */
    body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f4f4f9;
        color: #333;
    }

    .container {
        max-width: 600px;
        margin: 50px auto;
        padding: 20px;
        background: #ffffff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
    }

    h1 {
        text-align: center;
        margin-bottom: 20px;
        color: #4CAF50;
    }

    label {
        display: block;
        font-weight: bold;
        margin-bottom: 5px;
    }

    input[type="text"],
    input[type="password"],
    input[type="file"],
    button {
        width: 100%;
        padding: 10px;
        margin-bottom: 15px;
        border: 1px solid #ccc;
        border-radius: 5px;
        font-size: 16px;
    }

    button {
        background-color: #4CAF50;
        color: #ffffff;
        border: none;
        cursor: pointer;
        font-weight: bold;
    }

    button:hover {
        background-color: #45a049;
    }

    /* Media Query for Smaller Screens */
    @media (max-width: 480px) {
        .container {
            padding: 15px;
        }

        h1 {
            font-size: 1.5rem;
        }

        input[type="text"],
        input[type="password"],
        input[type="file"],
        button {
            font-size: 14px;
            padding: 8px;
        }
    }
</style>

<div class="container">
    <h1>Edit Profile</h1>
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        {% endfor %}
    {% endif %}
    <form method="POST" enctype="multipart/form-data" action="{% url 'userauth:profile' %}">
        {% csrf_token %}
        <label for="full_name">Full Name:</label>
        <input type="text" id="full_name" name="full_name" value="{{ profile.full_name }}" required>

        <label for="username">Username:</label>
        <input type="text" id="username" name="username" value="{{ request.user.username }}" required>

        <label for="mobile">Mobile:</label>
        <input type="text" id="mobile" name="mobile" value="{{ request.user.mobile }}" required>

        <label for="password">New Password (optional):</label>
        <input type="password" id="password" name="password">

        <label for="image">Profile Image:</label>
        <input type="file" id="image" name="image">

        <button type="submit">Save Changes</button>
    </form>
</div>


{% endblock %}

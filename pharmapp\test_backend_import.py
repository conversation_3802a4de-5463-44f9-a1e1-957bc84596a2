#!/usr/bin/env python
"""
Test script to verify the authentication backend can be imported.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

try:
    from userauth.backends import MobileBackend
    print("✅ Backend imported successfully!")
    
    # Test instantiation
    backend = MobileBackend()
    print("✅ Backend instantiated successfully!")
    
    print("\nBackend methods:")
    print(f"  - authenticate: {hasattr(backend, 'authenticate')}")
    print(f"  - get_user: {hasattr(backend, 'get_user')}")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Other error: {e}")

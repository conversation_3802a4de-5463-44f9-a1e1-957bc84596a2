{% extends 'partials/base.html' %}
{% load static %}

{% block extra_css %}
<style>
    .note-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border-left: 4px solid #007bff;
        margin-bottom: 1rem;
    }
    
    .note-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .note-card.pinned {
        border-left-color: #ffc107;
        background-color: #fffbf0;
    }
    
    .note-card.high-priority {
        border-left-color: #dc3545;
    }
    
    .note-card.urgent-priority {
        border-left-color: #dc3545;
        background-color: #fff5f5;
    }
    
    .note-content {
        max-height: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .note-tags {
        margin-top: 0.5rem;
    }
    
    .note-tag {
        display: inline-block;
        background-color: #e9ecef;
        color: #495057;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        text-decoration: none;
    }
    
    .note-tag:hover {
        background-color: #dee2e6;
        color: #495057;
        text-decoration: none;
    }
    
    .priority-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    .notebook-sidebar {
        background-color: #f8f9fa;
        border-right: 1px solid #dee2e6;
        min-height: calc(100vh - 200px);
    }
    
    .notebook-main {
        padding: 1rem;
    }
    
    .search-form {
        background-color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .reminder-overdue {
        background-color: #fff5f5;
        border-left: 4px solid #dc3545;
    }
    
    .reminder-upcoming {
        background-color: #f0f9ff;
        border-left: 4px solid #3b82f6;
    }
    
    .category-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }
    
    .notebook-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .btn-notebook {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
    }
    
    .btn-notebook:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="notebook-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-0">
                    <i class="fas fa-book"></i> 
                    {% block notebook_title %}Notebook{% endblock %}
                </h1>
                <p class="mb-0">{% block notebook_subtitle %}Organize your thoughts and ideas{% endblock %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 col-lg-2 notebook-sidebar">
            <div class="p-3">
                <h6 class="text-muted mb-3">NOTEBOOK MENU</h6>
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" 
                           href="{% url 'notebook:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'note_list' %}active{% endif %}" 
                           href="{% url 'notebook:note_list' %}">
                            <i class="fas fa-sticky-note"></i> All Notes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'note_create' %}active{% endif %}" 
                           href="{% url 'notebook:note_create' %}">
                            <i class="fas fa-plus"></i> New Note
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'archived_notes' %}active{% endif %}" 
                           href="{% url 'notebook:archived_notes' %}">
                            <i class="fas fa-archive"></i> Archived
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'category_list' %}active{% endif %}" 
                           href="{% url 'notebook:category_list' %}">
                            <i class="fas fa-tags"></i> Categories
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 notebook-main">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            {% block notebook_content %}
            {% endblock %}
        </div>
    </div>
</div>
{% endblock %}

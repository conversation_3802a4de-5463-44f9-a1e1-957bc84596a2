# Generated by Django 5.1.5 on 2025-02-02 08:36

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('phone', models.CharField(blank=True, max_length=15, null=True)),
                ('contact_info', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Procurement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('total', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='supplier.supplier')),
            ],
        ),
        migrations.CreateModel(
            name='ProcurementItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_name', models.CharField(max_length=255)),
                ('dosage_form', models.CharField(choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal')], default='dosage_form', max_length=255)),
                ('brand', models.CharField(blank=True, default='None', max_length=225, null=True)),
                ('unit', models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=100)),
                ('quantity', models.PositiveIntegerField(default=0)),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('subtotal', models.DecimalField(decimal_places=2, editable=False, max_digits=10)),
                ('procurement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='supplier.procurement')),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleProcurement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('total', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='supplier.supplier')),
            ],
        ),
        migrations.CreateModel(
            name='WholesaleProcurementItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_name', models.CharField(max_length=255)),
                ('dosage_form', models.CharField(choices=[('Tablet', 'Tablet'), ('Capsule', 'Capsule'), ('Cream', 'Cream'), ('Consumable', 'Consumable'), ('Injection', 'Injection'), ('Infusion', 'Infusion'), ('Inhaler', 'Inhaler'), ('Suspension', 'Suspension'), ('Syrup', 'Syrup'), ('Eye-drop', 'Eye-drop'), ('Ear-drop', 'Ear-drop'), ('Eye-ointment', 'Eye-ointment'), ('Rectal', 'Rectal'), ('Vaginal', 'Vaginal')], default='dosage_form', max_length=255)),
                ('brand', models.CharField(blank=True, default='None', max_length=225, null=True)),
                ('unit', models.CharField(choices=[('Amp', 'Amp'), ('Bottle', 'Bottle'), ('Tab', 'Tab'), ('Tin', 'Tin'), ('Caps', 'Caps'), ('Card', 'Card'), ('Carton', 'Carton'), ('Pack', 'Pack'), ('Pcs', 'Pcs'), ('Roll', 'Roll'), ('Vail', 'Vail'), ('1L', '1L'), ('2L', '2L'), ('4L', '4L')], max_length=100)),
                ('quantity', models.PositiveIntegerField(default=0)),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('subtotal', models.DecimalField(decimal_places=2, editable=False, max_digits=10)),
                ('procurement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='supplier.wholesaleprocurement')),
            ],
        ),
    ]

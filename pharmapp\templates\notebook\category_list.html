{% extends 'notebook/base_notebook.html' %}
{% load static %}

{% block notebook_title %}Note Categories{% endblock %}
{% block notebook_subtitle %}Manage and organize your note categories{% endblock %}

{% block notebook_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="mb-0">Categories ({{ categories|length }})</h5>
    <a href="{% url 'notebook:category_create' %}" class="btn btn-notebook">
        <i class="fas fa-plus"></i> New Category
    </a>
</div>

{% if categories %}
    <div class="row">
        {% for category in categories %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <span class="category-color me-3" 
                                  style="background-color: {{ category.color }}; width: 30px; height: 30px; border-radius: 50%;"></span>
                            <h5 class="card-title mb-0">{{ category.name }}</h5>
                        </div>
                        
                        {% if category.description %}
                            <p class="card-text text-muted">{{ category.description|truncatechars:100 }}</p>
                        {% else %}
                            <p class="card-text text-muted">No description provided.</p>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-sticky-note"></i> 
                                {{ category.note_set.count }} note{{ category.note_set.count|pluralize }}
                            </small>
                            <div>
                                <a href="{% url 'notebook:note_list' %}?category={{ category.id }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View Notes
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> Created {{ category.created_at|timesince }} ago
                        </small>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-tags fa-5x text-muted mb-4"></i>
        <h4 class="text-muted">No categories found</h4>
        <p class="text-muted">Create categories to better organize your notes.</p>
        <a href="{% url 'notebook:category_create' %}" class="btn btn-notebook btn-lg">
            <i class="fas fa-plus"></i> Create Your First Category
        </a>
    </div>
{% endif %}
{% endblock %}

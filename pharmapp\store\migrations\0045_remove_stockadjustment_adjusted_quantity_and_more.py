# Generated by Django 5.1.7 on 2025-04-19 23:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0044_alter_salesitem_quantity_alter_wholesalesalesitem_quantity'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='stockadjustment',
            name='adjusted_quantity',
        ),
        migrations.RemoveField(
            model_name='wholesalestockadjustment',
            name='adjusted_quantity',
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='adjustment_type',
            field=models.CharField(choices=[('manual', 'Manual Adjustment'), ('stock_check', 'Stock Check Adjustment'), ('transfer', 'Transfer Adjustment'), ('other', 'Other')], default='manual', max_length=20),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='item',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='stock_adjustments', to='store.item'),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='new_quantity',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='old_quantity',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='wholesalestockadjustment',
            name='adjustment_type',
            field=models.CharField(choices=[('manual', 'Manual Adjustment'), ('stock_check', 'Stock Check Adjustment'), ('transfer', 'Transfer Adjustment'), ('other', 'Other')], default='manual', max_length=20),
        ),
        migrations.AddField(
            model_name='wholesalestockadjustment',
            name='item',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='stock_adjustments', to='store.wholesaleitem'),
        ),
        migrations.AddField(
            model_name='wholesalestockadjustment',
            name='new_quantity',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='wholesalestockadjustment',
            name='notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='wholesalestockadjustment',
            name='old_quantity',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='stockadjustment',
            name='stock_check_item',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.stockcheckitem'),
        ),
        migrations.AlterField(
            model_name='wholesalestockadjustment',
            name='stock_check_item',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.wholesalestockcheckitem'),
        ),
    ]

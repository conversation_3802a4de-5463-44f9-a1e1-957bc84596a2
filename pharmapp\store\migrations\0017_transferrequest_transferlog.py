# Generated by Django 5.1.5 on 2025-02-15 20:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('store', '0016_alter_wholesalestockcheckitem_stock_check'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TransferRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_object_id', models.PositiveIntegerField()),
                ('destination_object_id', models.PositiveIntegerField()),
                ('requested_quantity', models.PositiveIntegerField()),
                ('approved_quantity', models.PositiveIntegerField(blank=True, null=True)),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected'), ('Completed', 'Completed')], default='Pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_transfers', to=settings.AUTH_USER_MODEL)),
                ('destination_content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='destination_transfers', to='contenttypes.contenttype')),
                ('source_content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_transfers', to='contenttypes.contenttype')),
            ],
        ),
        migrations.CreateModel(
            name='TransferLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transferred_quantity', models.PositiveIntegerField()),
                ('transferred_at', models.DateTimeField(auto_now_add=True)),
                ('transferred_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('transfer_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='log', to='store.transferrequest')),
            ],
        ),
    ]

!function(u){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=u();else if("function"==typeof define&&define.amd)define([],u);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).XRegExp=u()}}((function(){return function r(u,d,t){function o(c,i){if(!d[c]){if(!u[c]){var l="function"==typeof require&&require;if(!i&&l)return l(c,!0);if(a)return a(c,!0);var D=new Error("Cannot find module '"+c+"'");throw D.code="MODULE_NOT_FOUND",D}var p=d[c]={exports:{}};u[c][0].call(p.exports,(function(d){return o(u[c][1][d]||d)}),p,p.exports,r,u,d,t)}return d[c].exports}for(var a="function"==typeof require&&require,c=0;c<t.length;c++)o(t[c]);return o}({1:[function(u,d,t){"use strict";var a=u("@babel/runtime-corejs3/core-js-stable/instance/slice"),c=u("@babel/runtime-corejs3/core-js-stable/array/from"),i=u("@babel/runtime-corejs3/core-js-stable/symbol"),l=u("@babel/runtime-corejs3/core-js/get-iterator-method"),D=u("@babel/runtime-corejs3/core-js-stable/array/is-array"),p=u("@babel/runtime-corejs3/core-js-stable/object/define-property"),b=u("@babel/runtime-corejs3/helpers/interopRequireDefault");p(t,"__esModule",{value:!0}),t.default=void 0;var y=b(u("@babel/runtime-corejs3/helpers/slicedToArray")),m=b(u("@babel/runtime-corejs3/core-js-stable/instance/for-each")),A=b(u("@babel/runtime-corejs3/core-js-stable/instance/concat")),E=b(u("@babel/runtime-corejs3/core-js-stable/instance/index-of"));function _createForOfIteratorHelper(u,d){var t=void 0!==i&&l(u)||u["@@iterator"];if(!t){if(D(u)||(t=function _unsupportedIterableToArray(u,d){var t;if(!u)return;if("string"==typeof u)return _arrayLikeToArray(u,d);var i=a(t=Object.prototype.toString.call(u)).call(t,8,-1);"Object"===i&&u.constructor&&(i=u.constructor.name);if("Map"===i||"Set"===i)return c(u);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return _arrayLikeToArray(u,d)}(u))||d&&u&&"number"==typeof u.length){t&&(u=t);var p=0,b=function F(){};return{s:b,n:function n(){return p>=u.length?{done:!0}:{done:!1,value:u[p++]}},e:function e(u){throw u},f:b}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var y,m=!0,A=!1;return{s:function s(){t=t.call(u)},n:function n(){var u=t.next();return m=u.done,u},e:function e(u){A=!0,y=u},f:function f(){try{m||null==t.return||t.return()}finally{if(A)throw y}}}}function _arrayLikeToArray(u,d){(null==d||d>u.length)&&(d=u.length);for(var t=0,a=new Array(d);t<d;t++)a[t]=u[t];return a}
/*!
 * XRegExp Unicode Base 5.1.1
 * <xregexp.com>
 * Steven Levithan (c) 2008-present MIT License
 */t.default=function _default(u){var d={},t={},a=u._dec,c=u._hex,i=u._pad4;function normalize(u){return u.replace(/[- _]+/g,"").toLowerCase()}function charCode(u){var d=/^\\[xu](.+)/.exec(u);return d?a(d[1]):u.charCodeAt("\\"===u[0]?1:0)}function cacheInvertedBmp(t){return d[t]["b!"]||(d[t]["b!"]=function invertBmp(d){var t="",a=-1;return(0,m.default)(u).call(u,d,/(\\x..|\\u....|\\?[\s\S])(?:-(\\x..|\\u....|\\?[\s\S]))?/,(function(u){var d=charCode(u[1]);d>a+1&&(t+="\\u".concat(i(c(a+1))),d>a+2&&(t+="-\\u".concat(i(c(d-1))))),a=charCode(u[2]||u[1])})),a<65535&&(t+="\\u".concat(i(c(a+1))),a<65534&&(t+="-\\uFFFF")),t}(d[t].bmp))}function cacheAstral(u,t){var a=t?"a!":"a=";return d[u][a]||(d[u][a]=function buildAstral(u,t){var a,c,i=d[u],l="";return i.bmp&&!i.isBmpLast&&(l=(0,A.default)(a="[".concat(i.bmp,"]")).call(a,i.astral?"|":"")),i.astral&&(l+=i.astral),i.isBmpLast&&i.bmp&&(l+=(0,A.default)(c="".concat(i.astral?"|":"","[")).call(c,i.bmp,"]")),t?"(?:(?!".concat(l,")(?:[\ud800-\udbff][\udc00-\udfff]|[\0-￿]))"):"(?:".concat(l,")")}(u,t))}u.addToken(/\\([pP])(?:{(\^?)(?:(\w+)=)?([^}]*)}|([A-Za-z]))/,(function(u,a,c){var i="Unknown Unicode token ",l=(0,y.default)(u,6),D=l[0],p=l[1],b=l[2],m=l[3],C=l[4],g=l[5],h="P"===p||!!b,x=-1!==(0,E.default)(c).call(c,"A"),v=normalize(g||C),B=d[v];if("P"===p&&b)throw new SyntaxError("Invalid double negation "+D);if(!d.hasOwnProperty(v))throw new SyntaxError(i+D);if(m&&(!t[m]||!t[m][v]))throw new SyntaxError(i+D);if(B.inverseOf){var w;if(v=normalize(B.inverseOf),!d.hasOwnProperty(v))throw new ReferenceError((0,A.default)(w="".concat("Unicode token missing data "+D," -> ")).call(w,B.inverseOf));B=d[v],h=!h}if(!B.bmp&&!x)throw new SyntaxError("Astral mode required for Unicode token "+D);if(x){if("class"===a)throw new SyntaxError("Astral mode does not support Unicode tokens within character classes");return cacheAstral(v,h)}return"class"===a?h?cacheInvertedBmp(v):B.bmp:"".concat((h?"[^":"[")+B.bmp,"]")}),{scope:"all",optionalFlags:"A",leadChar:"\\"}),u.addUnicodeData=function(a,c){c&&(t[c]={});var i,l=_createForOfIteratorHelper(a);try{for(l.s();!(i=l.n()).done;){var D=i.value;if(!D.name)throw new Error("Unicode token requires name");if(!(D.inverseOf||D.bmp||D.astral))throw new Error("Unicode token has no character data "+D.name);var p=normalize(D.name);if(d[p]=D,c&&(t[c][p]=!0),D.alias){var b=normalize(D.alias);d[b]=D,c&&(t[c][b]=!0)}}}catch(u){l.e(u)}finally{l.f()}u.cache.flush("patterns")},u._getUnicodeProperty=function(u){var t=normalize(u);return d[t]}},d.exports=t.default},{"@babel/runtime-corejs3/core-js-stable/array/from":5,"@babel/runtime-corejs3/core-js-stable/array/is-array":6,"@babel/runtime-corejs3/core-js-stable/instance/concat":7,"@babel/runtime-corejs3/core-js-stable/instance/for-each":9,"@babel/runtime-corejs3/core-js-stable/instance/index-of":10,"@babel/runtime-corejs3/core-js-stable/instance/slice":11,"@babel/runtime-corejs3/core-js-stable/object/define-property":14,"@babel/runtime-corejs3/core-js-stable/symbol":16,"@babel/runtime-corejs3/core-js/get-iterator-method":19,"@babel/runtime-corejs3/helpers/interopRequireDefault":24,"@babel/runtime-corejs3/helpers/slicedToArray":27}],2:[function(u,d,t){"use strict";var a=u("@babel/runtime-corejs3/core-js-stable/object/define-property"),c=u("@babel/runtime-corejs3/helpers/interopRequireDefault");a(t,"__esModule",{value:!0}),t.default=void 0;var i=c(u("../../tools/output/categories"));
/*!
 * XRegExp Unicode Categories 5.1.1
 * <xregexp.com>
 * Steven Levithan (c) 2010-present MIT License
 * Unicode data by Mathias Bynens <mathiasbynens.be>
 */t.default=function _default(u){if(!u.addUnicodeData)throw new ReferenceError("Unicode Base must be loaded before Unicode Categories");u.addUnicodeData(i.default)},d.exports=t.default},{"../../tools/output/categories":222,"@babel/runtime-corejs3/core-js-stable/object/define-property":14,"@babel/runtime-corejs3/helpers/interopRequireDefault":24}],3:[function(u,d,t){"use strict";var a=u("@babel/runtime-corejs3/core-js-stable/object/define-property"),c=u("@babel/runtime-corejs3/helpers/interopRequireDefault");a(t,"__esModule",{value:!0}),t.default=void 0;var i=c(u("./xregexp")),l=c(u("./addons/unicode-base")),D=c(u("./addons/unicode-categories"));(0,l.default)(i.default),(0,D.default)(i.default);var p=i.default;t.default=p,d.exports=t.default},{"./addons/unicode-base":1,"./addons/unicode-categories":2,"./xregexp":4,"@babel/runtime-corejs3/core-js-stable/object/define-property":14,"@babel/runtime-corejs3/helpers/interopRequireDefault":24}],4:[function(u,d,t){"use strict";var a=u("@babel/runtime-corejs3/core-js-stable/instance/slice"),c=u("@babel/runtime-corejs3/core-js-stable/array/from"),i=u("@babel/runtime-corejs3/core-js-stable/symbol"),l=u("@babel/runtime-corejs3/core-js/get-iterator-method"),D=u("@babel/runtime-corejs3/core-js-stable/array/is-array"),p=u("@babel/runtime-corejs3/core-js-stable/object/define-property"),b=u("@babel/runtime-corejs3/helpers/interopRequireDefault");p(t,"__esModule",{value:!0}),t.default=void 0;var y=b(u("@babel/runtime-corejs3/helpers/slicedToArray")),m=b(u("@babel/runtime-corejs3/core-js-stable/instance/flags")),A=b(u("@babel/runtime-corejs3/core-js-stable/instance/sort")),E=b(u("@babel/runtime-corejs3/core-js-stable/instance/slice")),C=b(u("@babel/runtime-corejs3/core-js-stable/parse-int")),g=b(u("@babel/runtime-corejs3/core-js-stable/instance/index-of")),h=b(u("@babel/runtime-corejs3/core-js-stable/instance/for-each")),x=b(u("@babel/runtime-corejs3/core-js-stable/object/create")),v=b(u("@babel/runtime-corejs3/core-js-stable/instance/concat"));function _createForOfIteratorHelper(u,d){var t=void 0!==i&&l(u)||u["@@iterator"];if(!t){if(D(u)||(t=function _unsupportedIterableToArray(u,d){var t;if(!u)return;if("string"==typeof u)return _arrayLikeToArray(u,d);var i=a(t=Object.prototype.toString.call(u)).call(t,8,-1);"Object"===i&&u.constructor&&(i=u.constructor.name);if("Map"===i||"Set"===i)return c(u);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return _arrayLikeToArray(u,d)}(u))||d&&u&&"number"==typeof u.length){t&&(u=t);var p=0,b=function F(){};return{s:b,n:function n(){return p>=u.length?{done:!0}:{done:!1,value:u[p++]}},e:function e(u){throw u},f:b}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var y,m=!0,A=!1;return{s:function s(){t=t.call(u)},n:function n(){var u=t.next();return m=u.done,u},e:function e(u){A=!0,y=u},f:function f(){try{m||null==t.return||t.return()}finally{if(A)throw y}}}}function _arrayLikeToArray(u,d){(null==d||d>u.length)&&(d=u.length);for(var t=0,a=new Array(d);t<d;t++)a[t]=u[t];return a}
/*!
 * XRegExp 5.1.1
 * <xregexp.com>
 * Steven Levithan (c) 2007-present MIT License
 */var B={astral:!1,namespacing:!0},w={},j={},k={},S=[],O="default",R="class",_={default:/\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9]\d*|x[\dA-Fa-f]{2}|u(?:[\dA-Fa-f]{4}|{[\dA-Fa-f]+})|c[A-Za-z]|[\s\S])|\(\?(?:[:=!]|<[=!])|[?*+]\?|{\d+(?:,\d*)?}\??|[\s\S]/,class:/\\(?:[0-3][0-7]{0,2}|[4-7][0-7]?|x[\dA-Fa-f]{2}|u(?:[\dA-Fa-f]{4}|{[\dA-Fa-f]+})|c[A-Za-z]|[\s\S])|[\s\S]/},T=/\$(?:\{([^\}]+)\}|<([^>]+)>|(\d\d?|[\s\S]?))/g,I=void 0===/()??/.exec("")[1],P=void 0!==(0,m.default)(/x/);function hasNativeFlag(u){var d=!0;try{if(new RegExp("",u),"y"===u){".."===".a".replace(new RegExp("a","gy"),".")&&(d=!1)}}catch(u){d=!1}return d}var X=hasNativeFlag("d"),L=hasNativeFlag("s"),N=hasNativeFlag("u"),M=hasNativeFlag("y"),U={d:X,g:!0,i:!0,m:!0,s:L,u:N,y:M},G=L?/[^dgimsuy]+/g:/[^dgimuy]+/g;function augment(u,d,t,a,c){var i;if(u.xregexp={captureNames:d},c)return u;if(u.__proto__)u.__proto__=XRegExp.prototype;else for(var l in XRegExp.prototype)u[l]=XRegExp.prototype[l];return u.xregexp.source=t,u.xregexp.flags=a?(0,A.default)(i=a.split("")).call(i).join(""):a,u}function clipDuplicates(u){return u.replace(/([\s\S])(?=[\s\S]*\1)/g,"")}function copyRegex(u,d){var t;if(!XRegExp.isRegExp(u))throw new TypeError("Type RegExp expected");var a=u.xregexp||{},c=function getNativeFlags(u){return P?(0,m.default)(u):/\/([a-z]*)$/i.exec(RegExp.prototype.toString.call(u))[1]}(u),i="",l="",D=null,p=null;return(d=d||{}).removeG&&(l+="g"),d.removeY&&(l+="y"),l&&(c=c.replace(new RegExp("[".concat(l,"]+"),"g"),"")),d.addG&&(i+="g"),d.addY&&(i+="y"),i&&(c=clipDuplicates(c+i)),d.isInternalOnly||(void 0!==a.source&&(D=a.source),null!=(0,m.default)(a)&&(p=i?clipDuplicates((0,m.default)(a)+i):(0,m.default)(a))),u=augment(new RegExp(d.source||u.source,c),function hasNamedCapture(u){return!(!u.xregexp||!u.xregexp.captureNames)}(u)?(0,E.default)(t=a.captureNames).call(t,0):null,D,p,d.isInternalOnly)}function dec(u){return(0,C.default)(u,16)}function getContextualTokenSeparator(u,d,t){var a=u.index+u[0].length,c=u.input[u.index-1],i=u.input[a];return/^[()|]$/.test(c)||/^[()|]$/.test(i)||0===u.index||a===u.input.length||/\(\?(?:[:=!]|<[=!])$/.test(u.input.substring(u.index-4,u.index))||function isQuantifierNext(u,d,t){return(-1!==(0,g.default)(t).call(t,"x")?/^(?:\s|#[^#\n]*|\(\?#[^)]*\))*(?:[?*+]|{\d+(?:,\d*)?})/:/^(?:\(\?#[^)]*\))*(?:[?*+]|{\d+(?:,\d*)?})/).test((0,E.default)(u).call(u,d))}(u.input,a,t)?"":"(?:)"}function hex(u){return(0,C.default)(u,10).toString(16)}function isType(u,d){return Object.prototype.toString.call(u)==="[object ".concat(d,"]")}function nullThrows(u){if(null==u)throw new TypeError("Cannot convert null or undefined to object");return u}function pad4(u){for(;u.length<4;)u="0".concat(u);return u}function prepareOptions(u){var d={};return isType(u,"String")?((0,h.default)(XRegExp).call(XRegExp,u,/[^\s,]+/,(function(u){d[u]=!0})),d):u}function registerFlag(u){if(!/^[\w$]$/.test(u))throw new Error("Flag must be a single character A-Za-z0-9_$");U[u]=!0}function runTokens(u,d,t,a,c){for(var i,l,D=S.length,p=u[t],b=null;D--;)if(!((l=S[D]).leadChar&&l.leadChar!==p||l.scope!==a&&"all"!==l.scope||l.flag&&-1===(0,g.default)(d).call(d,l.flag))&&(i=XRegExp.exec(u,l.regex,t,"sticky"))){b={matchLength:i[0].length,output:l.handler.call(c,i,a,d),reparse:l.reparse};break}return b}function setAstral(u){B.astral=u}function setNamespacing(u){B.namespacing=u}function XRegExp(u,d){if(XRegExp.isRegExp(u)){if(void 0!==d)throw new TypeError("Cannot supply flags when copying a RegExp");return copyRegex(u)}if(u=void 0===u?"":String(u),d=void 0===d?"":String(d),XRegExp.isInstalled("astral")&&-1===(0,g.default)(d).call(d,"A")&&(d+="A"),k[u]||(k[u]={}),!k[u][d]){for(var t,a={hasNamedCapture:!1,captureNames:[]},c=O,i="",l=0,D=function prepareFlags(u,d){if(clipDuplicates(d)!==d)throw new SyntaxError("Invalid duplicate regex flag ".concat(d));u=u.replace(/^\(\?([\w$]+)\)/,(function(u,t){if(/[dgy]/.test(t))throw new SyntaxError("Cannot use flags dgy in mode modifier ".concat(u));return d=clipDuplicates(d+t),""}));var t,a=_createForOfIteratorHelper(d);try{for(a.s();!(t=a.n()).done;){var c=t.value;if(!U[c])throw new SyntaxError("Unknown regex flag ".concat(c))}}catch(u){a.e(u)}finally{a.f()}return{pattern:u,flags:d}}(u,d),p=D.pattern,b=(0,m.default)(D);l<p.length;){do{(t=runTokens(p,b,l,c,a))&&t.reparse&&(p=(0,E.default)(p).call(p,0,l)+t.output+(0,E.default)(p).call(p,l+t.matchLength))}while(t&&t.reparse);if(t)i+=t.output,l+=t.matchLength||1;else{var A=XRegExp.exec(p,_[c],l,"sticky"),C=(0,y.default)(A,1)[0];i+=C,l+=C.length,"["===C&&c===O?c=R:"]"===C&&c===R&&(c=O)}}k[u][d]={pattern:i.replace(/(?:\(\?:\))+/g,"(?:)"),flags:b.replace(G,""),captures:a.hasNamedCapture?a.captureNames:null}}var h=k[u][d];return augment(new RegExp(h.pattern,(0,m.default)(h)),h.captures,u,d)}XRegExp.prototype=/(?:)/,XRegExp.version="5.1.1",XRegExp._clipDuplicates=clipDuplicates,XRegExp._hasNativeFlag=hasNativeFlag,XRegExp._dec=dec,XRegExp._hex=hex,XRegExp._pad4=pad4,XRegExp.addToken=function(u,d,t){var a=(t=t||{}).optionalFlags;if(t.flag&&registerFlag(t.flag),a){var c,i=_createForOfIteratorHelper(a=a.split(""));try{for(i.s();!(c=i.n()).done;){registerFlag(c.value)}}catch(u){i.e(u)}finally{i.f()}}S.push({regex:copyRegex(u,{addG:!0,addY:M,isInternalOnly:!0}),handler:d,scope:t.scope||O,flag:t.flag,reparse:t.reparse,leadChar:t.leadChar}),XRegExp.cache.flush("patterns")},XRegExp.cache=function(u,d){return j[u]||(j[u]={}),j[u][d]||(j[u][d]=XRegExp(u,d))},XRegExp.cache.flush=function(u){"patterns"===u?k={}:j={}},XRegExp.escape=function(u){return String(nullThrows(u)).replace(/[\\\[\]{}()*+?.^$|]/g,"\\$&").replace(/[\s#\-,]/g,(function(u){return"\\u".concat(pad4(hex(u.charCodeAt(0))))}))},XRegExp.exec=function(u,d,t,a){var c,i,l="g",D=!1;(c=M&&!!(a||d.sticky&&!1!==a))?l+="y":a&&(D=!0,l+="FakeY"),d.xregexp=d.xregexp||{};var p=d.xregexp[l]||(d.xregexp[l]=copyRegex(d,{addG:!0,addY:c,source:D?"".concat(d.source,"|()"):void 0,removeY:!1===a,isInternalOnly:!0}));return t=t||0,p.lastIndex=t,i=w.exec.call(p,u),D&&i&&""===i.pop()&&(i=null),d.global&&(d.lastIndex=i?p.lastIndex:0),i},XRegExp.forEach=function(u,d,t){for(var a,c=0,i=-1;a=XRegExp.exec(u,d,c);)t(a,++i,u,d),c=a.index+(a[0].length||1)},XRegExp.globalize=function(u){return copyRegex(u,{addG:!0})},XRegExp.install=function(u){u=prepareOptions(u),!B.astral&&u.astral&&setAstral(!0),!B.namespacing&&u.namespacing&&setNamespacing(!0)},XRegExp.isInstalled=function(u){return!!B[u]},XRegExp.isRegExp=function(u){return"[object RegExp]"===Object.prototype.toString.call(u)},XRegExp.match=function(u,d,t){var a=d.global&&"one"!==t||"all"===t,c=(a?"g":"")+(d.sticky?"y":"")||"noGY";d.xregexp=d.xregexp||{};var i=d.xregexp[c]||(d.xregexp[c]=copyRegex(d,{addG:!!a,removeG:"one"===t,isInternalOnly:!0})),l=String(nullThrows(u)).match(i);return d.global&&(d.lastIndex="one"===t&&l?l.index+l[0].length:0),a?l||[]:l&&l[0]},XRegExp.matchChain=function(u,d){return function recurseChain(u,t){var a=d[t].regex?d[t]:{regex:d[t]},c=[];function addMatch(u){if(a.backref){var d="Backreference to undefined group: ".concat(a.backref),t=isNaN(a.backref);if(t&&XRegExp.isInstalled("namespacing")){if(!u.groups||!(a.backref in u.groups))throw new ReferenceError(d)}else if(!u.hasOwnProperty(a.backref))throw new ReferenceError(d);var i=t&&XRegExp.isInstalled("namespacing")?u.groups[a.backref]:u[a.backref];c.push(i||"")}else c.push(u[0])}var i,l=_createForOfIteratorHelper(u);try{for(l.s();!(i=l.n()).done;){var D=i.value;(0,h.default)(XRegExp).call(XRegExp,D,a.regex,addMatch)}}catch(u){l.e(u)}finally{l.f()}return t!==d.length-1&&c.length?recurseChain(c,t+1):c}([u],0)},XRegExp.replace=function(u,d,t,a){var c=XRegExp.isRegExp(d),i=d.global&&"one"!==a||"all"===a,l=(i?"g":"")+(d.sticky?"y":"")||"noGY",D=d;c?(d.xregexp=d.xregexp||{},D=d.xregexp[l]||(d.xregexp[l]=copyRegex(d,{addG:!!i,removeG:"one"===a,isInternalOnly:!0}))):i&&(D=new RegExp(XRegExp.escape(String(d)),"g"));var p=w.replace.call(nullThrows(u),D,t);return c&&d.global&&(d.lastIndex=0),p},XRegExp.replaceEach=function(u,d){var t,a=_createForOfIteratorHelper(d);try{for(a.s();!(t=a.n()).done;){var c=t.value;u=XRegExp.replace(u,c[0],c[1],c[2])}}catch(u){a.e(u)}finally{a.f()}return u},XRegExp.split=function(u,d,t){return w.split.call(nullThrows(u),d,t)},XRegExp.test=function(u,d,t,a){return!!XRegExp.exec(u,d,t,a)},XRegExp.uninstall=function(u){u=prepareOptions(u),B.astral&&u.astral&&setAstral(!1),B.namespacing&&u.namespacing&&setNamespacing(!1)},XRegExp.union=function(u,d,t){var a,c,i=(t=t||{}).conjunction||"or",l=0;function rewrite(u,d,t){var i=c[l-a];if(d){if(++l,i)return"(?<".concat(i,">")}else if(t)return"\\".concat(+t+a);return u}if(!isType(u,"Array")||!u.length)throw new TypeError("Must provide a nonempty array of patterns to merge");var D,p=/(\()(?!\?)|\\([1-9]\d*)|\\[\s\S]|\[(?:[^\\\]]|\\[\s\S])*\]/g,b=[],y=_createForOfIteratorHelper(u);try{for(y.s();!(D=y.n()).done;){var m=D.value;XRegExp.isRegExp(m)?(a=l,c=m.xregexp&&m.xregexp.captureNames||[],b.push(XRegExp(m.source).source.replace(p,rewrite))):b.push(XRegExp.escape(m))}}catch(u){y.e(u)}finally{y.f()}var A="none"===i?"":"|";return XRegExp(b.join(A),d)},w.exec=function(u){var d=this.lastIndex,t=RegExp.prototype.exec.apply(this,arguments);if(t){if(!I&&t.length>1&&-1!==(0,g.default)(t).call(t,"")){var a,c=copyRegex(this,{removeG:!0,isInternalOnly:!0});(0,E.default)(a=String(u)).call(a,t.index).replace(c,(function(){for(var u=arguments.length,d=1;d<u-2;++d)void 0===(d<0||arguments.length<=d?void 0:arguments[d])&&(t[d]=void 0)}))}if(this.xregexp&&this.xregexp.captureNames){var i=t;XRegExp.isInstalled("namespacing")&&(t.groups=(0,x.default)(null),i=t.groups);for(var l=1;l<t.length;++l){var D=this.xregexp.captureNames[l-1];D&&(i[D]=t[l])}}else!t.groups&&XRegExp.isInstalled("namespacing")&&(t.groups=void 0);this.global&&!t[0].length&&this.lastIndex>t.index&&(this.lastIndex=t.index)}return this.global||(this.lastIndex=d),t},w.test=function(u){return!!w.exec.call(this,u)},w.match=function(u){if(XRegExp.isRegExp(u)){if(u.global){var d=String.prototype.match.apply(this,arguments);return u.lastIndex=0,d}}else u=new RegExp(u);return w.exec.call(u,nullThrows(this))},w.replace=function(u,d){var t,a,c,i=XRegExp.isRegExp(u);return i?(u.xregexp&&(a=u.xregexp.captureNames),t=u.lastIndex):u+="",c=isType(d,"Function")?String(this).replace(u,(function(){for(var u=arguments.length,t=new Array(u),c=0;c<u;c++)t[c]=arguments[c];if(a){var i;XRegExp.isInstalled("namespacing")?(i=(0,x.default)(null),t.push(i)):(t[0]=new String(t[0]),i=t[0]);for(var l=0;l<a.length;++l)a[l]&&(i[a[l]]=t[l+1])}return d.apply(void 0,t)})):String(nullThrows(this)).replace(u,(function(){for(var u=arguments.length,t=new Array(u),c=0;c<u;c++)t[c]=arguments[c];return String(d).replace(T,replacer);function replacer(u,d,c,i){d=d||c;var l,D,p=isType(t[t.length-1],"Object")?4:3,b=t.length-p;if(d){if(/^\d+$/.test(d)){var y=+d;if(y<=b)return t[y]||""}var m=a?(0,g.default)(a).call(a,d):-1;if(m<0)throw new SyntaxError("Backreference to undefined group ".concat(u));return t[m+1]||""}if(""===i||" "===i)throw new SyntaxError("Invalid token ".concat(u));if("&"===i||0==+i)return t[0];if("$"===i)return"$";if("`"===i)return(0,E.default)(l=t[t.length-1]).call(l,0,t[t.length-2]);if("'"===i)return(0,E.default)(D=t[t.length-1]).call(D,t[t.length-2]+t[0].length);if(i=+i,!isNaN(i)){if(i>b)throw new SyntaxError("Backreference to undefined group ".concat(u));return t[i]||""}throw new SyntaxError("Invalid token ".concat(u))}})),i&&(u.global?u.lastIndex=0:u.lastIndex=t),c},w.split=function(u,d){if(!XRegExp.isRegExp(u))return String.prototype.split.apply(this,arguments);var t,a=String(this),c=[],i=u.lastIndex,l=0;return d=(void 0===d?-1:d)>>>0,(0,h.default)(XRegExp).call(XRegExp,a,u,(function(u){u.index+u[0].length>l&&(c.push((0,E.default)(a).call(a,l,u.index)),u.length>1&&u.index<a.length&&Array.prototype.push.apply(c,(0,E.default)(u).call(u,1)),t=u[0].length,l=u.index+t)})),l===a.length?u.test("")&&!t||c.push(""):c.push((0,E.default)(a).call(a,l)),u.lastIndex=i,c.length>d?(0,E.default)(c).call(c,0,d):c},XRegExp.addToken(/\\([ABCE-RTUVXYZaeg-mopqyz]|c(?![A-Za-z])|u(?![\dA-Fa-f]{4}|{[\dA-Fa-f]+})|x(?![\dA-Fa-f]{2}))/,(function(u,d){if("B"===u[1]&&d===O)return u[0];throw new SyntaxError("Invalid escape ".concat(u[0]))}),{scope:"all",leadChar:"\\"}),XRegExp.addToken(/\\u{([\dA-Fa-f]+)}/,(function(u,d,t){var a=dec(u[1]);if(a>1114111)throw new SyntaxError("Invalid Unicode code point ".concat(u[0]));if(a<=65535)return"\\u".concat(pad4(hex(a)));if(N&&-1!==(0,g.default)(t).call(t,"u"))return u[0];throw new SyntaxError("Cannot use Unicode code point above \\u{FFFF} without flag u")}),{scope:"all",leadChar:"\\"}),XRegExp.addToken(/\(\?#[^)]*\)/,getContextualTokenSeparator,{leadChar:"("}),XRegExp.addToken(/\s+|#[^\n]*\n?/,getContextualTokenSeparator,{flag:"x"}),L||XRegExp.addToken(/\./,(function(){return"[\\s\\S]"}),{flag:"s",leadChar:"."}),XRegExp.addToken(/\\k<([^>]+)>/,(function(u){var d,t,a=isNaN(u[1])?(0,g.default)(d=this.captureNames).call(d,u[1])+1:+u[1],c=u.index+u[0].length;if(!a||a>this.captureNames.length)throw new SyntaxError("Backreference to undefined group ".concat(u[0]));return(0,v.default)(t="\\".concat(a)).call(t,c===u.input.length||isNaN(u.input[c])?"":"(?:)")}),{leadChar:"\\"}),XRegExp.addToken(/\\(\d+)/,(function(u,d){if(!(d===O&&/^[1-9]/.test(u[1])&&+u[1]<=this.captureNames.length)&&"0"!==u[1])throw new SyntaxError("Cannot use octal escape or backreference to undefined group ".concat(u[0]));return u[0]}),{scope:"all",leadChar:"\\"}),XRegExp.addToken(/\(\?P?<((?:[\$A-Z_a-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])(?:[\$0-9A-Z_a-z\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05EF-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u07FD\u0800-\u082D\u0840-\u085B\u0860-\u086A\u0870-\u0887\u0889-\u088E\u0898-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u09FE\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3C-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C5D\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDD\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D81-\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1715\u171F-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u1820-\u1878\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B4C\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CD0-\u1CD2\u1CD4-\u1CFA\u1D00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA827\uA82C\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD27\uDD30-\uDD39\uDE80-\uDEA9\uDEAB\uDEAC\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF50\uDF70-\uDF85\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC00-\uDC46\uDC66-\uDC75\uDC7F-\uDCBA\uDCC2\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD44-\uDD47\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDC9-\uDDCC\uDDCE-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3B-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC5E-\uDC61\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF1D-\uDF2B\uDF30-\uDF39\uDF40-\uDF46]|\uD806[\uDC00-\uDC3A\uDCA0-\uDCE9\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD35\uDD37\uDD38\uDD3B-\uDD43\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD7\uDDDA-\uDDE1\uDDE3\uDDE4\uDE00-\uDE3E\uDE47\uDE50-\uDE99\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD8E\uDD90\uDD91\uDD93-\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF6\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF4F-\uDF87\uDF8F-\uDF9F\uDFE0\uDFE1\uDFE3\uDFE4\uDFF0\uDFF1]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD833[\uDF00-\uDF2D\uDF30-\uDF46]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD837[\uDF00-\uDF1E]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDD00-\uDD2C\uDD30-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAE\uDEC0-\uDEF9]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4B\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A]|\uDB40[\uDD00-\uDDEF])*)>/,(function(u){var d;if(!XRegExp.isInstalled("namespacing")&&("length"===u[1]||"__proto__"===u[1]))throw new SyntaxError("Cannot use reserved word as capture name ".concat(u[0]));if(-1!==(0,g.default)(d=this.captureNames).call(d,u[1]))throw new SyntaxError("Cannot use same name for multiple groups ".concat(u[0]));return this.captureNames.push(u[1]),this.hasNamedCapture=!0,"("}),{leadChar:"("}),XRegExp.addToken(/\((?!\?)/,(function(u,d,t){return-1!==(0,g.default)(t).call(t,"n")?"(?:":(this.captureNames.push(null),"(")}),{optionalFlags:"n",leadChar:"("});var q=XRegExp;t.default=q,d.exports=t.default},{"@babel/runtime-corejs3/core-js-stable/array/from":5,"@babel/runtime-corejs3/core-js-stable/array/is-array":6,"@babel/runtime-corejs3/core-js-stable/instance/concat":7,"@babel/runtime-corejs3/core-js-stable/instance/flags":8,"@babel/runtime-corejs3/core-js-stable/instance/for-each":9,"@babel/runtime-corejs3/core-js-stable/instance/index-of":10,"@babel/runtime-corejs3/core-js-stable/instance/slice":11,"@babel/runtime-corejs3/core-js-stable/instance/sort":12,"@babel/runtime-corejs3/core-js-stable/object/create":13,"@babel/runtime-corejs3/core-js-stable/object/define-property":14,"@babel/runtime-corejs3/core-js-stable/parse-int":15,"@babel/runtime-corejs3/core-js-stable/symbol":16,"@babel/runtime-corejs3/core-js/get-iterator-method":19,"@babel/runtime-corejs3/helpers/interopRequireDefault":24,"@babel/runtime-corejs3/helpers/slicedToArray":27}],5:[function(u,d,t){d.exports=u("core-js-pure/stable/array/from")},{"core-js-pure/stable/array/from":208}],6:[function(u,d,t){d.exports=u("core-js-pure/stable/array/is-array")},{"core-js-pure/stable/array/is-array":209}],7:[function(u,d,t){d.exports=u("core-js-pure/stable/instance/concat")},{"core-js-pure/stable/instance/concat":212}],8:[function(u,d,t){d.exports=u("core-js-pure/stable/instance/flags")},{"core-js-pure/stable/instance/flags":213}],9:[function(u,d,t){d.exports=u("core-js-pure/stable/instance/for-each")},{"core-js-pure/stable/instance/for-each":214}],10:[function(u,d,t){d.exports=u("core-js-pure/stable/instance/index-of")},{"core-js-pure/stable/instance/index-of":215}],11:[function(u,d,t){d.exports=u("core-js-pure/stable/instance/slice")},{"core-js-pure/stable/instance/slice":216}],12:[function(u,d,t){d.exports=u("core-js-pure/stable/instance/sort")},{"core-js-pure/stable/instance/sort":217}],13:[function(u,d,t){d.exports=u("core-js-pure/stable/object/create")},{"core-js-pure/stable/object/create":218}],14:[function(u,d,t){d.exports=u("core-js-pure/stable/object/define-property")},{"core-js-pure/stable/object/define-property":219}],15:[function(u,d,t){d.exports=u("core-js-pure/stable/parse-int")},{"core-js-pure/stable/parse-int":220}],16:[function(u,d,t){d.exports=u("core-js-pure/stable/symbol")},{"core-js-pure/stable/symbol":221}],17:[function(u,d,t){d.exports=u("core-js-pure/features/array/from")},{"core-js-pure/features/array/from":52}],18:[function(u,d,t){d.exports=u("core-js-pure/features/array/is-array")},{"core-js-pure/features/array/is-array":53}],19:[function(u,d,t){d.exports=u("core-js-pure/features/get-iterator-method")},{"core-js-pure/features/get-iterator-method":54}],20:[function(u,d,t){d.exports=u("core-js-pure/features/instance/slice")},{"core-js-pure/features/instance/slice":55}],21:[function(u,d,t){d.exports=u("core-js-pure/features/symbol")},{"core-js-pure/features/symbol":56}],22:[function(u,d,t){d.exports=function _arrayLikeToArray(u,d){(null==d||d>u.length)&&(d=u.length);for(var t=0,a=new Array(d);t<d;t++)a[t]=u[t];return a},d.exports.__esModule=!0,d.exports.default=d.exports},{}],23:[function(u,d,t){var a=u("@babel/runtime-corejs3/core-js/array/is-array");d.exports=function _arrayWithHoles(u){if(a(u))return u},d.exports.__esModule=!0,d.exports.default=d.exports},{"@babel/runtime-corejs3/core-js/array/is-array":18}],24:[function(u,d,t){d.exports=function _interopRequireDefault(u){return u&&u.__esModule?u:{default:u}},d.exports.__esModule=!0,d.exports.default=d.exports},{}],25:[function(u,d,t){var a=u("@babel/runtime-corejs3/core-js/symbol"),c=u("@babel/runtime-corejs3/core-js/get-iterator-method");d.exports=function _iterableToArrayLimit(u,d){var t=null==u?null:void 0!==a&&c(u)||u["@@iterator"];if(null!=t){var i,l,D=[],p=!0,b=!1;try{for(t=t.call(u);!(p=(i=t.next()).done)&&(D.push(i.value),!d||D.length!==d);p=!0);}catch(u){b=!0,l=u}finally{try{p||null==t.return||t.return()}finally{if(b)throw l}}return D}},d.exports.__esModule=!0,d.exports.default=d.exports},{"@babel/runtime-corejs3/core-js/get-iterator-method":19,"@babel/runtime-corejs3/core-js/symbol":21}],26:[function(u,d,t){d.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},d.exports.__esModule=!0,d.exports.default=d.exports},{}],27:[function(u,d,t){var a=u("./arrayWithHoles.js"),c=u("./iterableToArrayLimit.js"),i=u("./unsupportedIterableToArray.js"),l=u("./nonIterableRest.js");d.exports=function _slicedToArray(u,d){return a(u)||c(u,d)||i(u,d)||l()},d.exports.__esModule=!0,d.exports.default=d.exports},{"./arrayWithHoles.js":23,"./iterableToArrayLimit.js":25,"./nonIterableRest.js":26,"./unsupportedIterableToArray.js":28}],28:[function(u,d,t){var a=u("@babel/runtime-corejs3/core-js/instance/slice"),c=u("@babel/runtime-corejs3/core-js/array/from"),i=u("./arrayLikeToArray.js");d.exports=function _unsupportedIterableToArray(u,d){var t;if(u){if("string"==typeof u)return i(u,d);var l=a(t=Object.prototype.toString.call(u)).call(t,8,-1);return"Object"===l&&u.constructor&&(l=u.constructor.name),"Map"===l||"Set"===l?c(u):"Arguments"===l||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?i(u,d):void 0}},d.exports.__esModule=!0,d.exports.default=d.exports},{"./arrayLikeToArray.js":22,"@babel/runtime-corejs3/core-js/array/from":17,"@babel/runtime-corejs3/core-js/instance/slice":20}],29:[function(u,d,t){var a=u("../../stable/array/from");d.exports=a},{"../../stable/array/from":208}],30:[function(u,d,t){var a=u("../../stable/array/is-array");d.exports=a},{"../../stable/array/is-array":209}],31:[function(u,d,t){var a=u("../stable/get-iterator-method");d.exports=a},{"../stable/get-iterator-method":211}],32:[function(u,d,t){var a=u("../../stable/instance/slice");d.exports=a},{"../../stable/instance/slice":216}],33:[function(u,d,t){var a=u("../../stable/symbol");d.exports=a},{"../../stable/symbol":221}],34:[function(u,d,t){u("../../modules/es.string.iterator"),u("../../modules/es.array.from");var a=u("../../internals/path");d.exports=a.Array.from},{"../../internals/path":142,"../../modules/es.array.from":170,"../../modules/es.string.iterator":184}],35:[function(u,d,t){u("../../modules/es.array.is-array");var a=u("../../internals/path");d.exports=a.Array.isArray},{"../../internals/path":142,"../../modules/es.array.is-array":172}],36:[function(u,d,t){u("../../../modules/es.array.concat");var a=u("../../../internals/entry-virtual");d.exports=a("Array").concat},{"../../../internals/entry-virtual":91,"../../../modules/es.array.concat":168}],37:[function(u,d,t){u("../../../modules/es.array.for-each");var a=u("../../../internals/entry-virtual");d.exports=a("Array").forEach},{"../../../internals/entry-virtual":91,"../../../modules/es.array.for-each":169}],38:[function(u,d,t){u("../../../modules/es.array.index-of");var a=u("../../../internals/entry-virtual");d.exports=a("Array").indexOf},{"../../../internals/entry-virtual":91,"../../../modules/es.array.index-of":171}],39:[function(u,d,t){u("../../../modules/es.array.slice");var a=u("../../../internals/entry-virtual");d.exports=a("Array").slice},{"../../../internals/entry-virtual":91,"../../../modules/es.array.slice":174}],40:[function(u,d,t){u("../../../modules/es.array.sort");var a=u("../../../internals/entry-virtual");d.exports=a("Array").sort},{"../../../internals/entry-virtual":91,"../../../modules/es.array.sort":175}],41:[function(u,d,t){u("../modules/es.array.iterator"),u("../modules/es.string.iterator");var a=u("../internals/get-iterator-method");d.exports=a},{"../internals/get-iterator-method":101,"../modules/es.array.iterator":173,"../modules/es.string.iterator":184}],42:[function(u,d,t){var a=u("../../internals/object-is-prototype-of"),c=u("../array/virtual/concat"),i=Array.prototype;d.exports=function(u){var d=u.concat;return u===i||a(i,u)&&d===i.concat?c:d}},{"../../internals/object-is-prototype-of":135,"../array/virtual/concat":36}],43:[function(u,d,t){var a=u("../../internals/object-is-prototype-of"),c=u("../regexp/flags"),i=RegExp.prototype;d.exports=function(u){return u===i||a(i,u)?c(u):u.flags}},{"../../internals/object-is-prototype-of":135,"../regexp/flags":50}],44:[function(u,d,t){var a=u("../../internals/object-is-prototype-of"),c=u("../array/virtual/index-of"),i=Array.prototype;d.exports=function(u){var d=u.indexOf;return u===i||a(i,u)&&d===i.indexOf?c:d}},{"../../internals/object-is-prototype-of":135,"../array/virtual/index-of":38}],45:[function(u,d,t){var a=u("../../internals/object-is-prototype-of"),c=u("../array/virtual/slice"),i=Array.prototype;d.exports=function(u){var d=u.slice;return u===i||a(i,u)&&d===i.slice?c:d}},{"../../internals/object-is-prototype-of":135,"../array/virtual/slice":39}],46:[function(u,d,t){var a=u("../../internals/object-is-prototype-of"),c=u("../array/virtual/sort"),i=Array.prototype;d.exports=function(u){var d=u.sort;return u===i||a(i,u)&&d===i.sort?c:d}},{"../../internals/object-is-prototype-of":135,"../array/virtual/sort":40}],47:[function(u,d,t){u("../../modules/es.object.create");var a=u("../../internals/path").Object;d.exports=function create(u,d){return a.create(u,d)}},{"../../internals/path":142,"../../modules/es.object.create":178}],48:[function(u,d,t){u("../../modules/es.object.define-property");var a=u("../../internals/path").Object,c=d.exports=function defineProperty(u,d,t){return a.defineProperty(u,d,t)};a.defineProperty.sham&&(c.sham=!0)},{"../../internals/path":142,"../../modules/es.object.define-property":179}],49:[function(u,d,t){u("../modules/es.parse-int");var a=u("../internals/path");d.exports=a.parseInt},{"../internals/path":142,"../modules/es.parse-int":181}],50:[function(u,d,t){u("../../modules/es.regexp.flags");var a=u("../../internals/function-uncurry-this"),c=u("../../internals/regexp-flags");d.exports=a(c)},{"../../internals/function-uncurry-this":99,"../../internals/regexp-flags":144,"../../modules/es.regexp.flags":183}],51:[function(u,d,t){u("../../modules/es.array.concat"),u("../../modules/es.object.to-string"),u("../../modules/es.symbol"),u("../../modules/es.symbol.async-iterator"),u("../../modules/es.symbol.description"),u("../../modules/es.symbol.has-instance"),u("../../modules/es.symbol.is-concat-spreadable"),u("../../modules/es.symbol.iterator"),u("../../modules/es.symbol.match"),u("../../modules/es.symbol.match-all"),u("../../modules/es.symbol.replace"),u("../../modules/es.symbol.search"),u("../../modules/es.symbol.species"),u("../../modules/es.symbol.split"),u("../../modules/es.symbol.to-primitive"),u("../../modules/es.symbol.to-string-tag"),u("../../modules/es.symbol.unscopables"),u("../../modules/es.json.to-string-tag"),u("../../modules/es.math.to-string-tag"),u("../../modules/es.reflect.to-string-tag");var a=u("../../internals/path");d.exports=a.Symbol},{"../../internals/path":142,"../../modules/es.array.concat":168,"../../modules/es.json.to-string-tag":176,"../../modules/es.math.to-string-tag":177,"../../modules/es.object.to-string":180,"../../modules/es.reflect.to-string-tag":182,"../../modules/es.symbol":190,"../../modules/es.symbol.async-iterator":185,"../../modules/es.symbol.description":186,"../../modules/es.symbol.has-instance":187,"../../modules/es.symbol.is-concat-spreadable":188,"../../modules/es.symbol.iterator":189,"../../modules/es.symbol.match":192,"../../modules/es.symbol.match-all":191,"../../modules/es.symbol.replace":193,"../../modules/es.symbol.search":194,"../../modules/es.symbol.species":195,"../../modules/es.symbol.split":196,"../../modules/es.symbol.to-primitive":197,"../../modules/es.symbol.to-string-tag":198,"../../modules/es.symbol.unscopables":199}],52:[function(u,d,t){var a=u("../../actual/array/from");d.exports=a},{"../../actual/array/from":29}],53:[function(u,d,t){var a=u("../../actual/array/is-array");d.exports=a},{"../../actual/array/is-array":30}],54:[function(u,d,t){var a=u("../actual/get-iterator-method");d.exports=a},{"../actual/get-iterator-method":31}],55:[function(u,d,t){var a=u("../../actual/instance/slice");d.exports=a},{"../../actual/instance/slice":32}],56:[function(u,d,t){var a=u("../../actual/symbol");u("../../modules/esnext.symbol.async-dispose"),u("../../modules/esnext.symbol.dispose"),u("../../modules/esnext.symbol.matcher"),u("../../modules/esnext.symbol.metadata"),u("../../modules/esnext.symbol.observable"),u("../../modules/esnext.symbol.pattern-match"),u("../../modules/esnext.symbol.replace-all"),d.exports=a},{"../../actual/symbol":33,"../../modules/esnext.symbol.async-dispose":200,"../../modules/esnext.symbol.dispose":201,"../../modules/esnext.symbol.matcher":202,"../../modules/esnext.symbol.metadata":203,"../../modules/esnext.symbol.observable":204,"../../modules/esnext.symbol.pattern-match":205,"../../modules/esnext.symbol.replace-all":206}],57:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/is-callable"),i=u("../internals/try-to-string"),l=a.TypeError;d.exports=function(u){if(c(u))return u;throw l(i(u)+" is not a function")}},{"../internals/global":104,"../internals/is-callable":114,"../internals/try-to-string":162}],58:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/is-callable"),i=a.String,l=a.TypeError;d.exports=function(u){if("object"==typeof u||c(u))return u;throw l("Can't set "+i(u)+" as a prototype")}},{"../internals/global":104,"../internals/is-callable":114}],59:[function(u,d,t){d.exports=function(){}},{}],60:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/is-object"),i=a.String,l=a.TypeError;d.exports=function(u){if(c(u))return u;throw l(i(u)+" is not an object")}},{"../internals/global":104,"../internals/is-object":117}],61:[function(u,d,t){"use strict";var a=u("../internals/array-iteration").forEach,c=u("../internals/array-method-is-strict")("forEach");d.exports=c?[].forEach:function forEach(u){return a(this,u,arguments.length>1?arguments[1]:void 0)}},{"../internals/array-iteration":64,"../internals/array-method-is-strict":66}],62:[function(u,d,t){"use strict";var a=u("../internals/global"),c=u("../internals/function-bind-context"),i=u("../internals/function-call"),l=u("../internals/to-object"),D=u("../internals/call-with-safe-iteration-closing"),p=u("../internals/is-array-iterator-method"),b=u("../internals/is-constructor"),y=u("../internals/length-of-array-like"),m=u("../internals/create-property"),A=u("../internals/get-iterator"),E=u("../internals/get-iterator-method"),C=a.Array;d.exports=function from(u){var d=l(u),t=b(this),a=arguments.length,g=a>1?arguments[1]:void 0,h=void 0!==g;h&&(g=c(g,a>2?arguments[2]:void 0));var x,v,B,w,j,k,S=E(d),O=0;if(!S||this==C&&p(S))for(x=y(d),v=t?new this(x):C(x);x>O;O++)k=h?g(d[O],O):d[O],m(v,O,k);else for(j=(w=A(d,S)).next,v=t?new this:[];!(B=i(j,w)).done;O++)k=h?D(w,g,[B.value,O],!0):B.value,m(v,O,k);return v.length=O,v}},{"../internals/call-with-safe-iteration-closing":72,"../internals/create-property":80,"../internals/function-bind-context":96,"../internals/function-call":97,"../internals/get-iterator":102,"../internals/get-iterator-method":101,"../internals/global":104,"../internals/is-array-iterator-method":112,"../internals/is-constructor":115,"../internals/length-of-array-like":123,"../internals/to-object":157}],63:[function(u,d,t){var a=u("../internals/to-indexed-object"),c=u("../internals/to-absolute-index"),i=u("../internals/length-of-array-like"),createMethod=function(u){return function(d,t,l){var D,p=a(d),b=i(p),y=c(l,b);if(u&&t!=t){for(;b>y;)if((D=p[y++])!=D)return!0}else for(;b>y;y++)if((u||y in p)&&p[y]===t)return u||y||0;return!u&&-1}};d.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},{"../internals/length-of-array-like":123,"../internals/to-absolute-index":153,"../internals/to-indexed-object":154}],64:[function(u,d,t){var a=u("../internals/function-bind-context"),c=u("../internals/function-uncurry-this"),i=u("../internals/indexed-object"),l=u("../internals/to-object"),D=u("../internals/length-of-array-like"),p=u("../internals/array-species-create"),b=c([].push),createMethod=function(u){var d=1==u,t=2==u,c=3==u,y=4==u,m=6==u,A=7==u,E=5==u||m;return function(C,g,h,x){for(var v,B,w=l(C),j=i(w),k=a(g,h),S=D(j),O=0,R=x||p,_=d?R(C,S):t||A?R(C,0):void 0;S>O;O++)if((E||O in j)&&(B=k(v=j[O],O,w),u))if(d)_[O]=B;else if(B)switch(u){case 3:return!0;case 5:return v;case 6:return O;case 2:b(_,v)}else switch(u){case 4:return!1;case 7:b(_,v)}return m?-1:c||y?y:_}};d.exports={forEach:createMethod(0),map:createMethod(1),filter:createMethod(2),some:createMethod(3),every:createMethod(4),find:createMethod(5),findIndex:createMethod(6),filterReject:createMethod(7)}},{"../internals/array-species-create":71,"../internals/function-bind-context":96,"../internals/function-uncurry-this":99,"../internals/indexed-object":109,"../internals/length-of-array-like":123,"../internals/to-object":157}],65:[function(u,d,t){var a=u("../internals/fails"),c=u("../internals/well-known-symbol"),i=u("../internals/engine-v8-version"),l=c("species");d.exports=function(u){return i>=51||!a((function(){var d=[];return(d.constructor={})[l]=function(){return{foo:1}},1!==d[u](Boolean).foo}))}},{"../internals/engine-v8-version":89,"../internals/fails":94,"../internals/well-known-symbol":166}],66:[function(u,d,t){"use strict";var a=u("../internals/fails");d.exports=function(u,d){var t=[][u];return!!t&&a((function(){t.call(null,d||function(){throw 1},1)}))}},{"../internals/fails":94}],67:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/to-absolute-index"),i=u("../internals/length-of-array-like"),l=u("../internals/create-property"),D=a.Array,p=Math.max;d.exports=function(u,d,t){for(var a=i(u),b=c(d,a),y=c(void 0===t?a:t,a),m=D(p(y-b,0)),A=0;b<y;b++,A++)l(m,A,u[b]);return m.length=A,m}},{"../internals/create-property":80,"../internals/global":104,"../internals/length-of-array-like":123,"../internals/to-absolute-index":153}],68:[function(u,d,t){var a=u("../internals/function-uncurry-this");d.exports=a([].slice)},{"../internals/function-uncurry-this":99}],69:[function(u,d,t){var a=u("../internals/array-slice-simple"),c=Math.floor,mergeSort=function(u,d){var t=u.length,i=c(t/2);return t<8?insertionSort(u,d):merge(u,mergeSort(a(u,0,i),d),mergeSort(a(u,i),d),d)},insertionSort=function(u,d){for(var t,a,c=u.length,i=1;i<c;){for(a=i,t=u[i];a&&d(u[a-1],t)>0;)u[a]=u[--a];a!==i++&&(u[a]=t)}return u},merge=function(u,d,t,a){for(var c=d.length,i=t.length,l=0,D=0;l<c||D<i;)u[l+D]=l<c&&D<i?a(d[l],t[D])<=0?d[l++]:t[D++]:l<c?d[l++]:t[D++];return u};d.exports=mergeSort},{"../internals/array-slice-simple":67}],70:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/is-array"),i=u("../internals/is-constructor"),l=u("../internals/is-object"),D=u("../internals/well-known-symbol")("species"),p=a.Array;d.exports=function(u){var d;return c(u)&&(d=u.constructor,(i(d)&&(d===p||c(d.prototype))||l(d)&&null===(d=d[D]))&&(d=void 0)),void 0===d?p:d}},{"../internals/global":104,"../internals/is-array":113,"../internals/is-constructor":115,"../internals/is-object":117,"../internals/well-known-symbol":166}],71:[function(u,d,t){var a=u("../internals/array-species-constructor");d.exports=function(u,d){return new(a(u))(0===d?0:d)}},{"../internals/array-species-constructor":70}],72:[function(u,d,t){var a=u("../internals/an-object"),c=u("../internals/iterator-close");d.exports=function(u,d,t,i){try{return i?d(a(t)[0],t[1]):d(t)}catch(d){c(u,"throw",d)}}},{"../internals/an-object":60,"../internals/iterator-close":120}],73:[function(u,d,t){var a=u("../internals/well-known-symbol")("iterator"),c=!1;try{var i=0,l={next:function(){return{done:!!i++}},return:function(){c=!0}};l[a]=function(){return this},Array.from(l,(function(){throw 2}))}catch(u){}d.exports=function(u,d){if(!d&&!c)return!1;var t=!1;try{var i={};i[a]=function(){return{next:function(){return{done:t=!0}}}},u(i)}catch(u){}return t}},{"../internals/well-known-symbol":166}],74:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=a({}.toString),i=a("".slice);d.exports=function(u){return i(c(u),8,-1)}},{"../internals/function-uncurry-this":99}],75:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/to-string-tag-support"),i=u("../internals/is-callable"),l=u("../internals/classof-raw"),D=u("../internals/well-known-symbol")("toStringTag"),p=a.Object,b="Arguments"==l(function(){return arguments}());d.exports=c?l:function(u){var d,t,a;return void 0===u?"Undefined":null===u?"Null":"string"==typeof(t=function(u,d){try{return u[d]}catch(u){}}(d=p(u),D))?t:b?l(d):"Object"==(a=l(d))&&i(d.callee)?"Arguments":a}},{"../internals/classof-raw":74,"../internals/global":104,"../internals/is-callable":114,"../internals/to-string-tag-support":160,"../internals/well-known-symbol":166}],76:[function(u,d,t){var a=u("../internals/fails");d.exports=!a((function(){function F(){}return F.prototype.constructor=null,Object.getPrototypeOf(new F)!==F.prototype}))},{"../internals/fails":94}],77:[function(u,d,t){"use strict";var a=u("../internals/iterators-core").IteratorPrototype,c=u("../internals/object-create"),i=u("../internals/create-property-descriptor"),l=u("../internals/set-to-string-tag"),D=u("../internals/iterators"),returnThis=function(){return this};d.exports=function(u,d,t,p){var b=d+" Iterator";return u.prototype=c(a,{next:i(+!p,t)}),l(u,b,!1,!0),D[b]=returnThis,u}},{"../internals/create-property-descriptor":79,"../internals/iterators":122,"../internals/iterators-core":121,"../internals/object-create":127,"../internals/set-to-string-tag":147}],78:[function(u,d,t){var a=u("../internals/descriptors"),c=u("../internals/object-define-property"),i=u("../internals/create-property-descriptor");d.exports=a?function(u,d,t){return c.f(u,d,i(1,t))}:function(u,d,t){return u[d]=t,u}},{"../internals/create-property-descriptor":79,"../internals/descriptors":83,"../internals/object-define-property":129}],79:[function(u,d,t){d.exports=function(u,d){return{enumerable:!(1&u),configurable:!(2&u),writable:!(4&u),value:d}}},{}],80:[function(u,d,t){"use strict";var a=u("../internals/to-property-key"),c=u("../internals/object-define-property"),i=u("../internals/create-property-descriptor");d.exports=function(u,d,t){var l=a(d);l in u?c.f(u,l,i(0,t)):u[l]=t}},{"../internals/create-property-descriptor":79,"../internals/object-define-property":129,"../internals/to-property-key":159}],81:[function(u,d,t){"use strict";var a=u("../internals/export"),c=u("../internals/function-call"),i=u("../internals/is-pure"),l=u("../internals/function-name"),D=u("../internals/is-callable"),p=u("../internals/create-iterator-constructor"),b=u("../internals/object-get-prototype-of"),y=u("../internals/object-set-prototype-of"),m=u("../internals/set-to-string-tag"),A=u("../internals/create-non-enumerable-property"),E=u("../internals/redefine"),C=u("../internals/well-known-symbol"),g=u("../internals/iterators"),h=u("../internals/iterators-core"),x=l.PROPER,v=l.CONFIGURABLE,B=h.IteratorPrototype,w=h.BUGGY_SAFARI_ITERATORS,j=C("iterator"),k="keys",S="values",O="entries",returnThis=function(){return this};d.exports=function(u,d,t,l,C,h,R){p(t,d,l);var _,T,I,getIterationMethod=function(u){if(u===C&&M)return M;if(!w&&u in L)return L[u];switch(u){case k:return function keys(){return new t(this,u)};case S:return function values(){return new t(this,u)};case O:return function entries(){return new t(this,u)}}return function(){return new t(this)}},P=d+" Iterator",X=!1,L=u.prototype,N=L[j]||L["@@iterator"]||C&&L[C],M=!w&&N||getIterationMethod(C),U="Array"==d&&L.entries||N;if(U&&(_=b(U.call(new u)))!==Object.prototype&&_.next&&(i||b(_)===B||(y?y(_,B):D(_[j])||E(_,j,returnThis)),m(_,P,!0,!0),i&&(g[P]=returnThis)),x&&C==S&&N&&N.name!==S&&(!i&&v?A(L,"name",S):(X=!0,M=function values(){return c(N,this)})),C)if(T={values:getIterationMethod(S),keys:h?M:getIterationMethod(k),entries:getIterationMethod(O)},R)for(I in T)(w||X||!(I in L))&&E(L,I,T[I]);else a({target:d,proto:!0,forced:w||X},T);return i&&!R||L[j]===M||E(L,j,M,{name:C}),g[d]=M,T}},{"../internals/create-iterator-constructor":77,"../internals/create-non-enumerable-property":78,"../internals/export":93,"../internals/function-call":97,"../internals/function-name":98,"../internals/is-callable":114,"../internals/is-pure":118,"../internals/iterators":122,"../internals/iterators-core":121,"../internals/object-get-prototype-of":134,"../internals/object-set-prototype-of":139,"../internals/redefine":143,"../internals/set-to-string-tag":147,"../internals/well-known-symbol":166}],82:[function(u,d,t){var a=u("../internals/path"),c=u("../internals/has-own-property"),i=u("../internals/well-known-symbol-wrapped"),l=u("../internals/object-define-property").f;d.exports=function(u){var d=a.Symbol||(a.Symbol={});c(d,u)||l(d,u,{value:i.f(u)})}},{"../internals/has-own-property":105,"../internals/object-define-property":129,"../internals/path":142,"../internals/well-known-symbol-wrapped":165}],83:[function(u,d,t){var a=u("../internals/fails");d.exports=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},{"../internals/fails":94}],84:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/is-object"),i=a.document,l=c(i)&&c(i.createElement);d.exports=function(u){return l?i.createElement(u):{}}},{"../internals/global":104,"../internals/is-object":117}],85:[function(u,d,t){d.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},{}],86:[function(u,d,t){var a=u("../internals/engine-user-agent").match(/firefox\/(\d+)/i);d.exports=!!a&&+a[1]},{"../internals/engine-user-agent":88}],87:[function(u,d,t){var a=u("../internals/engine-user-agent");d.exports=/MSIE|Trident/.test(a)},{"../internals/engine-user-agent":88}],88:[function(u,d,t){var a=u("../internals/get-built-in");d.exports=a("navigator","userAgent")||""},{"../internals/get-built-in":100}],89:[function(u,d,t){var a,c,i=u("../internals/global"),l=u("../internals/engine-user-agent"),D=i.process,p=i.Deno,b=D&&D.versions||p&&p.version,y=b&&b.v8;y&&(c=(a=y.split("."))[0]>0&&a[0]<4?1:+(a[0]+a[1])),!c&&l&&(!(a=l.match(/Edge\/(\d+)/))||a[1]>=74)&&(a=l.match(/Chrome\/(\d+)/))&&(c=+a[1]),d.exports=c},{"../internals/engine-user-agent":88,"../internals/global":104}],90:[function(u,d,t){var a=u("../internals/engine-user-agent").match(/AppleWebKit\/(\d+)\./);d.exports=!!a&&+a[1]},{"../internals/engine-user-agent":88}],91:[function(u,d,t){var a=u("../internals/path");d.exports=function(u){return a[u+"Prototype"]}},{"../internals/path":142}],92:[function(u,d,t){d.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},{}],93:[function(u,d,t){"use strict";var a=u("../internals/global"),c=u("../internals/function-apply"),i=u("../internals/function-uncurry-this"),l=u("../internals/is-callable"),D=u("../internals/object-get-own-property-descriptor").f,p=u("../internals/is-forced"),b=u("../internals/path"),y=u("../internals/function-bind-context"),m=u("../internals/create-non-enumerable-property"),A=u("../internals/has-own-property"),wrapConstructor=function(u){var Wrapper=function(d,t,a){if(this instanceof Wrapper){switch(arguments.length){case 0:return new u;case 1:return new u(d);case 2:return new u(d,t)}return new u(d,t,a)}return c(u,this,arguments)};return Wrapper.prototype=u.prototype,Wrapper};d.exports=function(u,d){var t,c,E,C,g,h,x,v,B=u.target,w=u.global,j=u.stat,k=u.proto,S=w?a:j?a[B]:(a[B]||{}).prototype,O=w?b:b[B]||m(b,B,{})[B],R=O.prototype;for(E in d)t=!p(w?E:B+(j?".":"#")+E,u.forced)&&S&&A(S,E),g=O[E],t&&(h=u.noTargetGet?(v=D(S,E))&&v.value:S[E]),C=t&&h?h:d[E],t&&typeof g==typeof C||(x=u.bind&&t?y(C,a):u.wrap&&t?wrapConstructor(C):k&&l(C)?i(C):C,(u.sham||C&&C.sham||g&&g.sham)&&m(x,"sham",!0),m(O,E,x),k&&(A(b,c=B+"Prototype")||m(b,c,{}),m(b[c],E,C),u.real&&R&&!R[E]&&m(R,E,C)))}},{"../internals/create-non-enumerable-property":78,"../internals/function-apply":95,"../internals/function-bind-context":96,"../internals/function-uncurry-this":99,"../internals/global":104,"../internals/has-own-property":105,"../internals/is-callable":114,"../internals/is-forced":116,"../internals/object-get-own-property-descriptor":130,"../internals/path":142}],94:[function(u,d,t){d.exports=function(u){try{return!!u()}catch(u){return!0}}},{}],95:[function(u,d,t){var a=Function.prototype,c=a.apply,i=a.bind,l=a.call;d.exports="object"==typeof Reflect&&Reflect.apply||(i?l.bind(c):function(){return l.apply(c,arguments)})},{}],96:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=u("../internals/a-callable"),i=a(a.bind);d.exports=function(u,d){return c(u),void 0===d?u:i?i(u,d):function(){return u.apply(d,arguments)}}},{"../internals/a-callable":57,"../internals/function-uncurry-this":99}],97:[function(u,d,t){var a=Function.prototype.call;d.exports=a.bind?a.bind(a):function(){return a.apply(a,arguments)}},{}],98:[function(u,d,t){var a=u("../internals/descriptors"),c=u("../internals/has-own-property"),i=Function.prototype,l=a&&Object.getOwnPropertyDescriptor,D=c(i,"name"),p=D&&"something"===function something(){}.name,b=D&&(!a||a&&l(i,"name").configurable);d.exports={EXISTS:D,PROPER:p,CONFIGURABLE:b}},{"../internals/descriptors":83,"../internals/has-own-property":105}],99:[function(u,d,t){var a=Function.prototype,c=a.bind,i=a.call,l=c&&c.bind(i);d.exports=c?function(u){return u&&l(i,u)}:function(u){return u&&function(){return i.apply(u,arguments)}}},{}],100:[function(u,d,t){var a=u("../internals/path"),c=u("../internals/global"),i=u("../internals/is-callable"),aFunction=function(u){return i(u)?u:void 0};d.exports=function(u,d){return arguments.length<2?aFunction(a[u])||aFunction(c[u]):a[u]&&a[u][d]||c[u]&&c[u][d]}},{"../internals/global":104,"../internals/is-callable":114,"../internals/path":142}],101:[function(u,d,t){var a=u("../internals/classof"),c=u("../internals/get-method"),i=u("../internals/iterators"),l=u("../internals/well-known-symbol")("iterator");d.exports=function(u){if(null!=u)return c(u,l)||c(u,"@@iterator")||i[a(u)]}},{"../internals/classof":75,"../internals/get-method":103,"../internals/iterators":122,"../internals/well-known-symbol":166}],102:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/function-call"),i=u("../internals/a-callable"),l=u("../internals/an-object"),D=u("../internals/try-to-string"),p=u("../internals/get-iterator-method"),b=a.TypeError;d.exports=function(u,d){var t=arguments.length<2?p(u):d;if(i(t))return l(c(t,u));throw b(D(u)+" is not iterable")}},{"../internals/a-callable":57,"../internals/an-object":60,"../internals/function-call":97,"../internals/get-iterator-method":101,"../internals/global":104,"../internals/try-to-string":162}],103:[function(u,d,t){var a=u("../internals/a-callable");d.exports=function(u,d){var t=u[d];return null==t?void 0:a(t)}},{"../internals/a-callable":57}],104:[function(u,d,t){(function(u){(function(){var check=function(u){return u&&u.Math==Math&&u};d.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof u&&u)||function(){return this}()||Function("return this")()}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],105:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=u("../internals/to-object"),i=a({}.hasOwnProperty);d.exports=Object.hasOwn||function hasOwn(u,d){return i(c(u),d)}},{"../internals/function-uncurry-this":99,"../internals/to-object":157}],106:[function(u,d,t){d.exports={}},{}],107:[function(u,d,t){var a=u("../internals/get-built-in");d.exports=a("document","documentElement")},{"../internals/get-built-in":100}],108:[function(u,d,t){var a=u("../internals/descriptors"),c=u("../internals/fails"),i=u("../internals/document-create-element");d.exports=!a&&!c((function(){return 7!=Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},{"../internals/descriptors":83,"../internals/document-create-element":84,"../internals/fails":94}],109:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/function-uncurry-this"),i=u("../internals/fails"),l=u("../internals/classof-raw"),D=a.Object,p=c("".split);d.exports=i((function(){return!D("z").propertyIsEnumerable(0)}))?function(u){return"String"==l(u)?p(u,""):D(u)}:D},{"../internals/classof-raw":74,"../internals/fails":94,"../internals/function-uncurry-this":99,"../internals/global":104}],110:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=u("../internals/is-callable"),i=u("../internals/shared-store"),l=a(Function.toString);c(i.inspectSource)||(i.inspectSource=function(u){return l(u)}),d.exports=i.inspectSource},{"../internals/function-uncurry-this":99,"../internals/is-callable":114,"../internals/shared-store":149}],111:[function(u,d,t){var a,c,i,l=u("../internals/native-weak-map"),D=u("../internals/global"),p=u("../internals/function-uncurry-this"),b=u("../internals/is-object"),y=u("../internals/create-non-enumerable-property"),m=u("../internals/has-own-property"),A=u("../internals/shared-store"),E=u("../internals/shared-key"),C=u("../internals/hidden-keys"),g="Object already initialized",h=D.TypeError,x=D.WeakMap;if(l||A.state){var v=A.state||(A.state=new x),B=p(v.get),w=p(v.has),j=p(v.set);a=function(u,d){if(w(v,u))throw new h(g);return d.facade=u,j(v,u,d),d},c=function(u){return B(v,u)||{}},i=function(u){return w(v,u)}}else{var k=E("state");C[k]=!0,a=function(u,d){if(m(u,k))throw new h(g);return d.facade=u,y(u,k,d),d},c=function(u){return m(u,k)?u[k]:{}},i=function(u){return m(u,k)}}d.exports={set:a,get:c,has:i,enforce:function(u){return i(u)?c(u):a(u,{})},getterFor:function(u){return function(d){var t;if(!b(d)||(t=c(d)).type!==u)throw h("Incompatible receiver, "+u+" required");return t}}}},{"../internals/create-non-enumerable-property":78,"../internals/function-uncurry-this":99,"../internals/global":104,"../internals/has-own-property":105,"../internals/hidden-keys":106,"../internals/is-object":117,"../internals/native-weak-map":125,"../internals/shared-key":148,"../internals/shared-store":149}],112:[function(u,d,t){var a=u("../internals/well-known-symbol"),c=u("../internals/iterators"),i=a("iterator"),l=Array.prototype;d.exports=function(u){return void 0!==u&&(c.Array===u||l[i]===u)}},{"../internals/iterators":122,"../internals/well-known-symbol":166}],113:[function(u,d,t){var a=u("../internals/classof-raw");d.exports=Array.isArray||function isArray(u){return"Array"==a(u)}},{"../internals/classof-raw":74}],114:[function(u,d,t){d.exports=function(u){return"function"==typeof u}},{}],115:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=u("../internals/fails"),i=u("../internals/is-callable"),l=u("../internals/classof"),D=u("../internals/get-built-in"),p=u("../internals/inspect-source"),noop=function(){},b=[],y=D("Reflect","construct"),m=/^\s*(?:class|function)\b/,A=a(m.exec),E=!m.exec(noop),C=function isConstructor(u){if(!i(u))return!1;try{return y(noop,b,u),!0}catch(u){return!1}},g=function isConstructor(u){if(!i(u))return!1;switch(l(u)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return E||!!A(m,p(u))}catch(u){return!0}};g.sham=!0,d.exports=!y||c((function(){var u;return C(C.call)||!C(Object)||!C((function(){u=!0}))||u}))?g:C},{"../internals/classof":75,"../internals/fails":94,"../internals/function-uncurry-this":99,"../internals/get-built-in":100,"../internals/inspect-source":110,"../internals/is-callable":114}],116:[function(u,d,t){var a=u("../internals/fails"),c=u("../internals/is-callable"),i=/#|\.prototype\./,isForced=function(u,d){var t=D[l(u)];return t==b||t!=p&&(c(d)?a(d):!!d)},l=isForced.normalize=function(u){return String(u).replace(i,".").toLowerCase()},D=isForced.data={},p=isForced.NATIVE="N",b=isForced.POLYFILL="P";d.exports=isForced},{"../internals/fails":94,"../internals/is-callable":114}],117:[function(u,d,t){var a=u("../internals/is-callable");d.exports=function(u){return"object"==typeof u?null!==u:a(u)}},{"../internals/is-callable":114}],118:[function(u,d,t){d.exports=!0},{}],119:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/get-built-in"),i=u("../internals/is-callable"),l=u("../internals/object-is-prototype-of"),D=u("../internals/use-symbol-as-uid"),p=a.Object;d.exports=D?function(u){return"symbol"==typeof u}:function(u){var d=c("Symbol");return i(d)&&l(d.prototype,p(u))}},{"../internals/get-built-in":100,"../internals/global":104,"../internals/is-callable":114,"../internals/object-is-prototype-of":135,"../internals/use-symbol-as-uid":164}],120:[function(u,d,t){var a=u("../internals/function-call"),c=u("../internals/an-object"),i=u("../internals/get-method");d.exports=function(u,d,t){var l,D;c(u);try{if(!(l=i(u,"return"))){if("throw"===d)throw t;return t}l=a(l,u)}catch(u){D=!0,l=u}if("throw"===d)throw t;if(D)throw l;return c(l),t}},{"../internals/an-object":60,"../internals/function-call":97,"../internals/get-method":103}],121:[function(u,d,t){"use strict";var a,c,i,l=u("../internals/fails"),D=u("../internals/is-callable"),p=u("../internals/object-create"),b=u("../internals/object-get-prototype-of"),y=u("../internals/redefine"),m=u("../internals/well-known-symbol"),A=u("../internals/is-pure"),E=m("iterator"),C=!1;[].keys&&("next"in(i=[].keys())?(c=b(b(i)))!==Object.prototype&&(a=c):C=!0),null==a||l((function(){var u={};return a[E].call(u)!==u}))?a={}:A&&(a=p(a)),D(a[E])||y(a,E,(function(){return this})),d.exports={IteratorPrototype:a,BUGGY_SAFARI_ITERATORS:C}},{"../internals/fails":94,"../internals/is-callable":114,"../internals/is-pure":118,"../internals/object-create":127,"../internals/object-get-prototype-of":134,"../internals/redefine":143,"../internals/well-known-symbol":166}],122:[function(u,d,t){arguments[4][106][0].apply(t,arguments)},{dup:106}],123:[function(u,d,t){var a=u("../internals/to-length");d.exports=function(u){return a(u.length)}},{"../internals/to-length":156}],124:[function(u,d,t){var a=u("../internals/engine-v8-version"),c=u("../internals/fails");d.exports=!!Object.getOwnPropertySymbols&&!c((function(){var u=Symbol();return!String(u)||!(Object(u)instanceof Symbol)||!Symbol.sham&&a&&a<41}))},{"../internals/engine-v8-version":89,"../internals/fails":94}],125:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/is-callable"),i=u("../internals/inspect-source"),l=a.WeakMap;d.exports=c(l)&&/native code/.test(i(l))},{"../internals/global":104,"../internals/inspect-source":110,"../internals/is-callable":114}],126:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/fails"),i=u("../internals/function-uncurry-this"),l=u("../internals/to-string"),D=u("../internals/string-trim").trim,p=u("../internals/whitespaces"),b=a.parseInt,y=a.Symbol,m=y&&y.iterator,A=/^[+-]?0x/i,E=i(A.exec),C=8!==b(p+"08")||22!==b(p+"0x16")||m&&!c((function(){b(Object(m))}));d.exports=C?function parseInt(u,d){var t=D(l(u));return b(t,d>>>0||(E(A,t)?16:10))}:b},{"../internals/fails":94,"../internals/function-uncurry-this":99,"../internals/global":104,"../internals/string-trim":152,"../internals/to-string":161,"../internals/whitespaces":167}],127:[function(u,d,t){var a,c=u("../internals/an-object"),i=u("../internals/object-define-properties"),l=u("../internals/enum-bug-keys"),D=u("../internals/hidden-keys"),p=u("../internals/html"),b=u("../internals/document-create-element"),y=u("../internals/shared-key"),m=y("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(u){return"<script>"+u+"</"+"script>"},NullProtoObjectViaActiveX=function(u){u.write(scriptTag("")),u.close();var d=u.parentWindow.Object;return u=null,d},NullProtoObject=function(){try{a=new ActiveXObject("htmlfile")}catch(u){}var u,d;NullProtoObject="undefined"!=typeof document?document.domain&&a?NullProtoObjectViaActiveX(a):((d=b("iframe")).style.display="none",p.appendChild(d),d.src=String("javascript:"),(u=d.contentWindow.document).open(),u.write(scriptTag("document.F=Object")),u.close(),u.F):NullProtoObjectViaActiveX(a);for(var t=l.length;t--;)delete NullProtoObject.prototype[l[t]];return NullProtoObject()};D[m]=!0,d.exports=Object.create||function create(u,d){var t;return null!==u?(EmptyConstructor.prototype=c(u),t=new EmptyConstructor,EmptyConstructor.prototype=null,t[m]=u):t=NullProtoObject(),void 0===d?t:i(t,d)}},{"../internals/an-object":60,"../internals/document-create-element":84,"../internals/enum-bug-keys":92,"../internals/hidden-keys":106,"../internals/html":107,"../internals/object-define-properties":128,"../internals/shared-key":148}],128:[function(u,d,t){var a=u("../internals/descriptors"),c=u("../internals/object-define-property"),i=u("../internals/an-object"),l=u("../internals/to-indexed-object"),D=u("../internals/object-keys");d.exports=a?Object.defineProperties:function defineProperties(u,d){i(u);for(var t,a=l(d),p=D(d),b=p.length,y=0;b>y;)c.f(u,t=p[y++],a[t]);return u}},{"../internals/an-object":60,"../internals/descriptors":83,"../internals/object-define-property":129,"../internals/object-keys":137,"../internals/to-indexed-object":154}],129:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/descriptors"),i=u("../internals/ie8-dom-define"),l=u("../internals/an-object"),D=u("../internals/to-property-key"),p=a.TypeError,b=Object.defineProperty;t.f=c?b:function defineProperty(u,d,t){if(l(u),d=D(d),l(t),i)try{return b(u,d,t)}catch(u){}if("get"in t||"set"in t)throw p("Accessors not supported");return"value"in t&&(u[d]=t.value),u}},{"../internals/an-object":60,"../internals/descriptors":83,"../internals/global":104,"../internals/ie8-dom-define":108,"../internals/to-property-key":159}],130:[function(u,d,t){var a=u("../internals/descriptors"),c=u("../internals/function-call"),i=u("../internals/object-property-is-enumerable"),l=u("../internals/create-property-descriptor"),D=u("../internals/to-indexed-object"),p=u("../internals/to-property-key"),b=u("../internals/has-own-property"),y=u("../internals/ie8-dom-define"),m=Object.getOwnPropertyDescriptor;t.f=a?m:function getOwnPropertyDescriptor(u,d){if(u=D(u),d=p(d),y)try{return m(u,d)}catch(u){}if(b(u,d))return l(!c(i.f,u,d),u[d])}},{"../internals/create-property-descriptor":79,"../internals/descriptors":83,"../internals/function-call":97,"../internals/has-own-property":105,"../internals/ie8-dom-define":108,"../internals/object-property-is-enumerable":138,"../internals/to-indexed-object":154,"../internals/to-property-key":159}],131:[function(u,d,t){var a=u("../internals/classof-raw"),c=u("../internals/to-indexed-object"),i=u("../internals/object-get-own-property-names").f,l=u("../internals/array-slice-simple"),D="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];d.exports.f=function getOwnPropertyNames(u){return D&&"Window"==a(u)?function(u){try{return i(u)}catch(u){return l(D)}}(u):i(c(u))}},{"../internals/array-slice-simple":67,"../internals/classof-raw":74,"../internals/object-get-own-property-names":132,"../internals/to-indexed-object":154}],132:[function(u,d,t){var a=u("../internals/object-keys-internal"),c=u("../internals/enum-bug-keys").concat("length","prototype");t.f=Object.getOwnPropertyNames||function getOwnPropertyNames(u){return a(u,c)}},{"../internals/enum-bug-keys":92,"../internals/object-keys-internal":136}],133:[function(u,d,t){t.f=Object.getOwnPropertySymbols},{}],134:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/has-own-property"),i=u("../internals/is-callable"),l=u("../internals/to-object"),D=u("../internals/shared-key"),p=u("../internals/correct-prototype-getter"),b=D("IE_PROTO"),y=a.Object,m=y.prototype;d.exports=p?y.getPrototypeOf:function(u){var d=l(u);if(c(d,b))return d[b];var t=d.constructor;return i(t)&&d instanceof t?t.prototype:d instanceof y?m:null}},{"../internals/correct-prototype-getter":76,"../internals/global":104,"../internals/has-own-property":105,"../internals/is-callable":114,"../internals/shared-key":148,"../internals/to-object":157}],135:[function(u,d,t){var a=u("../internals/function-uncurry-this");d.exports=a({}.isPrototypeOf)},{"../internals/function-uncurry-this":99}],136:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=u("../internals/has-own-property"),i=u("../internals/to-indexed-object"),l=u("../internals/array-includes").indexOf,D=u("../internals/hidden-keys"),p=a([].push);d.exports=function(u,d){var t,a=i(u),b=0,y=[];for(t in a)!c(D,t)&&c(a,t)&&p(y,t);for(;d.length>b;)c(a,t=d[b++])&&(~l(y,t)||p(y,t));return y}},{"../internals/array-includes":63,"../internals/function-uncurry-this":99,"../internals/has-own-property":105,"../internals/hidden-keys":106,"../internals/to-indexed-object":154}],137:[function(u,d,t){var a=u("../internals/object-keys-internal"),c=u("../internals/enum-bug-keys");d.exports=Object.keys||function keys(u){return a(u,c)}},{"../internals/enum-bug-keys":92,"../internals/object-keys-internal":136}],138:[function(u,d,t){"use strict";var a={}.propertyIsEnumerable,c=Object.getOwnPropertyDescriptor,i=c&&!a.call({1:2},1);t.f=i?function propertyIsEnumerable(u){var d=c(this,u);return!!d&&d.enumerable}:a},{}],139:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=u("../internals/an-object"),i=u("../internals/a-possible-prototype");d.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var u,d=!1,t={};try{(u=a(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(t,[]),d=t instanceof Array}catch(u){}return function setPrototypeOf(t,a){return c(t),i(a),d?u(t,a):t.__proto__=a,t}}():void 0)},{"../internals/a-possible-prototype":58,"../internals/an-object":60,"../internals/function-uncurry-this":99}],140:[function(u,d,t){"use strict";var a=u("../internals/to-string-tag-support"),c=u("../internals/classof");d.exports=a?{}.toString:function toString(){return"[object "+c(this)+"]"}},{"../internals/classof":75,"../internals/to-string-tag-support":160}],141:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/function-call"),i=u("../internals/is-callable"),l=u("../internals/is-object"),D=a.TypeError;d.exports=function(u,d){var t,a;if("string"===d&&i(t=u.toString)&&!l(a=c(t,u)))return a;if(i(t=u.valueOf)&&!l(a=c(t,u)))return a;if("string"!==d&&i(t=u.toString)&&!l(a=c(t,u)))return a;throw D("Can't convert object to primitive value")}},{"../internals/function-call":97,"../internals/global":104,"../internals/is-callable":114,"../internals/is-object":117}],142:[function(u,d,t){arguments[4][106][0].apply(t,arguments)},{dup:106}],143:[function(u,d,t){var a=u("../internals/create-non-enumerable-property");d.exports=function(u,d,t,c){c&&c.enumerable?u[d]=t:a(u,d,t)}},{"../internals/create-non-enumerable-property":78}],144:[function(u,d,t){"use strict";var a=u("../internals/an-object");d.exports=function(){var u=a(this),d="";return u.global&&(d+="g"),u.ignoreCase&&(d+="i"),u.multiline&&(d+="m"),u.dotAll&&(d+="s"),u.unicode&&(d+="u"),u.sticky&&(d+="y"),d}},{"../internals/an-object":60}],145:[function(u,d,t){var a=u("../internals/global").TypeError;d.exports=function(u){if(null==u)throw a("Can't call method on "+u);return u}},{"../internals/global":104}],146:[function(u,d,t){var a=u("../internals/global"),c=Object.defineProperty;d.exports=function(u,d){try{c(a,u,{value:d,configurable:!0,writable:!0})}catch(t){a[u]=d}return d}},{"../internals/global":104}],147:[function(u,d,t){var a=u("../internals/to-string-tag-support"),c=u("../internals/object-define-property").f,i=u("../internals/create-non-enumerable-property"),l=u("../internals/has-own-property"),D=u("../internals/object-to-string"),p=u("../internals/well-known-symbol")("toStringTag");d.exports=function(u,d,t,b){if(u){var y=t?u:u.prototype;l(y,p)||c(y,p,{configurable:!0,value:d}),b&&!a&&i(y,"toString",D)}}},{"../internals/create-non-enumerable-property":78,"../internals/has-own-property":105,"../internals/object-define-property":129,"../internals/object-to-string":140,"../internals/to-string-tag-support":160,"../internals/well-known-symbol":166}],148:[function(u,d,t){var a=u("../internals/shared"),c=u("../internals/uid"),i=a("keys");d.exports=function(u){return i[u]||(i[u]=c(u))}},{"../internals/shared":150,"../internals/uid":163}],149:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/set-global"),i="__core-js_shared__",l=a[i]||c(i,{});d.exports=l},{"../internals/global":104,"../internals/set-global":146}],150:[function(u,d,t){var a=u("../internals/is-pure"),c=u("../internals/shared-store");(d.exports=function(u,d){return c[u]||(c[u]=void 0!==d?d:{})})("versions",[]).push({version:"3.20.0",mode:a?"pure":"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})},{"../internals/is-pure":118,"../internals/shared-store":149}],151:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=u("../internals/to-integer-or-infinity"),i=u("../internals/to-string"),l=u("../internals/require-object-coercible"),D=a("".charAt),p=a("".charCodeAt),b=a("".slice),createMethod=function(u){return function(d,t){var a,y,m=i(l(d)),A=c(t),E=m.length;return A<0||A>=E?u?"":void 0:(a=p(m,A))<55296||a>56319||A+1===E||(y=p(m,A+1))<56320||y>57343?u?D(m,A):a:u?b(m,A,A+2):y-56320+(a-55296<<10)+65536}};d.exports={codeAt:createMethod(!1),charAt:createMethod(!0)}},{"../internals/function-uncurry-this":99,"../internals/require-object-coercible":145,"../internals/to-integer-or-infinity":155,"../internals/to-string":161}],152:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=u("../internals/require-object-coercible"),i=u("../internals/to-string"),l=u("../internals/whitespaces"),D=a("".replace),p="["+l+"]",b=RegExp("^"+p+p+"*"),y=RegExp(p+p+"*$"),createMethod=function(u){return function(d){var t=i(c(d));return 1&u&&(t=D(t,b,"")),2&u&&(t=D(t,y,"")),t}};d.exports={start:createMethod(1),end:createMethod(2),trim:createMethod(3)}},{"../internals/function-uncurry-this":99,"../internals/require-object-coercible":145,"../internals/to-string":161,"../internals/whitespaces":167}],153:[function(u,d,t){var a=u("../internals/to-integer-or-infinity"),c=Math.max,i=Math.min;d.exports=function(u,d){var t=a(u);return t<0?c(t+d,0):i(t,d)}},{"../internals/to-integer-or-infinity":155}],154:[function(u,d,t){var a=u("../internals/indexed-object"),c=u("../internals/require-object-coercible");d.exports=function(u){return a(c(u))}},{"../internals/indexed-object":109,"../internals/require-object-coercible":145}],155:[function(u,d,t){var a=Math.ceil,c=Math.floor;d.exports=function(u){var d=+u;return d!=d||0===d?0:(d>0?c:a)(d)}},{}],156:[function(u,d,t){var a=u("../internals/to-integer-or-infinity"),c=Math.min;d.exports=function(u){return u>0?c(a(u),9007199254740991):0}},{"../internals/to-integer-or-infinity":155}],157:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/require-object-coercible"),i=a.Object;d.exports=function(u){return i(c(u))}},{"../internals/global":104,"../internals/require-object-coercible":145}],158:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/function-call"),i=u("../internals/is-object"),l=u("../internals/is-symbol"),D=u("../internals/get-method"),p=u("../internals/ordinary-to-primitive"),b=u("../internals/well-known-symbol"),y=a.TypeError,m=b("toPrimitive");d.exports=function(u,d){if(!i(u)||l(u))return u;var t,a=D(u,m);if(a){if(void 0===d&&(d="default"),t=c(a,u,d),!i(t)||l(t))return t;throw y("Can't convert object to primitive value")}return void 0===d&&(d="number"),p(u,d)}},{"../internals/function-call":97,"../internals/get-method":103,"../internals/global":104,"../internals/is-object":117,"../internals/is-symbol":119,"../internals/ordinary-to-primitive":141,"../internals/well-known-symbol":166}],159:[function(u,d,t){var a=u("../internals/to-primitive"),c=u("../internals/is-symbol");d.exports=function(u){var d=a(u,"string");return c(d)?d:d+""}},{"../internals/is-symbol":119,"../internals/to-primitive":158}],160:[function(u,d,t){var a={};a[u("../internals/well-known-symbol")("toStringTag")]="z",d.exports="[object z]"===String(a)},{"../internals/well-known-symbol":166}],161:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/classof"),i=a.String;d.exports=function(u){if("Symbol"===c(u))throw TypeError("Cannot convert a Symbol value to a string");return i(u)}},{"../internals/classof":75,"../internals/global":104}],162:[function(u,d,t){var a=u("../internals/global").String;d.exports=function(u){try{return a(u)}catch(u){return"Object"}}},{"../internals/global":104}],163:[function(u,d,t){var a=u("../internals/function-uncurry-this"),c=0,i=Math.random(),l=a(1..toString);d.exports=function(u){return"Symbol("+(void 0===u?"":u)+")_"+l(++c+i,36)}},{"../internals/function-uncurry-this":99}],164:[function(u,d,t){var a=u("../internals/native-symbol");d.exports=a&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},{"../internals/native-symbol":124}],165:[function(u,d,t){var a=u("../internals/well-known-symbol");t.f=a},{"../internals/well-known-symbol":166}],166:[function(u,d,t){var a=u("../internals/global"),c=u("../internals/shared"),i=u("../internals/has-own-property"),l=u("../internals/uid"),D=u("../internals/native-symbol"),p=u("../internals/use-symbol-as-uid"),b=c("wks"),y=a.Symbol,m=y&&y.for,A=p?y:y&&y.withoutSetter||l;d.exports=function(u){if(!i(b,u)||!D&&"string"!=typeof b[u]){var d="Symbol."+u;D&&i(y,u)?b[u]=y[u]:b[u]=p&&m?m(d):A(d)}return b[u]}},{"../internals/global":104,"../internals/has-own-property":105,"../internals/native-symbol":124,"../internals/shared":150,"../internals/uid":163,"../internals/use-symbol-as-uid":164}],167:[function(u,d,t){d.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},{}],168:[function(u,d,t){"use strict";var a=u("../internals/export"),c=u("../internals/global"),i=u("../internals/fails"),l=u("../internals/is-array"),D=u("../internals/is-object"),p=u("../internals/to-object"),b=u("../internals/length-of-array-like"),y=u("../internals/create-property"),m=u("../internals/array-species-create"),A=u("../internals/array-method-has-species-support"),E=u("../internals/well-known-symbol"),C=u("../internals/engine-v8-version"),g=E("isConcatSpreadable"),h=9007199254740991,x="Maximum allowed index exceeded",v=c.TypeError,B=C>=51||!i((function(){var u=[];return u[g]=!1,u.concat()[0]!==u})),w=A("concat"),isConcatSpreadable=function(u){if(!D(u))return!1;var d=u[g];return void 0!==d?!!d:l(u)};a({target:"Array",proto:!0,forced:!B||!w},{concat:function concat(u){var d,t,a,c,i,l=p(this),D=m(l,0),A=0;for(d=-1,a=arguments.length;d<a;d++)if(isConcatSpreadable(i=-1===d?l:arguments[d])){if(A+(c=b(i))>h)throw v(x);for(t=0;t<c;t++,A++)t in i&&y(D,A,i[t])}else{if(A>=h)throw v(x);y(D,A++,i)}return D.length=A,D}})},{"../internals/array-method-has-species-support":65,"../internals/array-species-create":71,"../internals/create-property":80,"../internals/engine-v8-version":89,"../internals/export":93,"../internals/fails":94,"../internals/global":104,"../internals/is-array":113,"../internals/is-object":117,"../internals/length-of-array-like":123,"../internals/to-object":157,"../internals/well-known-symbol":166}],169:[function(u,d,t){"use strict";var a=u("../internals/export"),c=u("../internals/array-for-each");a({target:"Array",proto:!0,forced:[].forEach!=c},{forEach:c})},{"../internals/array-for-each":61,"../internals/export":93}],170:[function(u,d,t){var a=u("../internals/export"),c=u("../internals/array-from");a({target:"Array",stat:!0,forced:!u("../internals/check-correctness-of-iteration")((function(u){Array.from(u)}))},{from:c})},{"../internals/array-from":62,"../internals/check-correctness-of-iteration":73,"../internals/export":93}],171:[function(u,d,t){"use strict";var a=u("../internals/export"),c=u("../internals/function-uncurry-this"),i=u("../internals/array-includes").indexOf,l=u("../internals/array-method-is-strict"),D=c([].indexOf),p=!!D&&1/D([1],1,-0)<0,b=l("indexOf");a({target:"Array",proto:!0,forced:p||!b},{indexOf:function indexOf(u){var d=arguments.length>1?arguments[1]:void 0;return p?D(this,u,d)||0:i(this,u,d)}})},{"../internals/array-includes":63,"../internals/array-method-is-strict":66,"../internals/export":93,"../internals/function-uncurry-this":99}],172:[function(u,d,t){u("../internals/export")({target:"Array",stat:!0},{isArray:u("../internals/is-array")})},{"../internals/export":93,"../internals/is-array":113}],173:[function(u,d,t){"use strict";var a=u("../internals/to-indexed-object"),c=u("../internals/add-to-unscopables"),i=u("../internals/iterators"),l=u("../internals/internal-state"),D=u("../internals/object-define-property").f,p=u("../internals/define-iterator"),b=u("../internals/is-pure"),y=u("../internals/descriptors"),m="Array Iterator",A=l.set,E=l.getterFor(m);d.exports=p(Array,"Array",(function(u,d){A(this,{type:m,target:a(u),index:0,kind:d})}),(function(){var u=E(this),d=u.target,t=u.kind,a=u.index++;return!d||a>=d.length?(u.target=void 0,{value:void 0,done:!0}):"keys"==t?{value:a,done:!1}:"values"==t?{value:d[a],done:!1}:{value:[a,d[a]],done:!1}}),"values");var C=i.Arguments=i.Array;if(c("keys"),c("values"),c("entries"),!b&&y&&"values"!==C.name)try{D(C,"name",{value:"values"})}catch(u){}},{"../internals/add-to-unscopables":59,"../internals/define-iterator":81,"../internals/descriptors":83,"../internals/internal-state":111,"../internals/is-pure":118,"../internals/iterators":122,"../internals/object-define-property":129,"../internals/to-indexed-object":154}],174:[function(u,d,t){"use strict";var a=u("../internals/export"),c=u("../internals/global"),i=u("../internals/is-array"),l=u("../internals/is-constructor"),D=u("../internals/is-object"),p=u("../internals/to-absolute-index"),b=u("../internals/length-of-array-like"),y=u("../internals/to-indexed-object"),m=u("../internals/create-property"),A=u("../internals/well-known-symbol"),E=u("../internals/array-method-has-species-support"),C=u("../internals/array-slice"),g=E("slice"),h=A("species"),x=c.Array,v=Math.max;a({target:"Array",proto:!0,forced:!g},{slice:function slice(u,d){var t,a,c,A=y(this),E=b(A),g=p(u,E),B=p(void 0===d?E:d,E);if(i(A)&&(t=A.constructor,(l(t)&&(t===x||i(t.prototype))||D(t)&&null===(t=t[h]))&&(t=void 0),t===x||void 0===t))return C(A,g,B);for(a=new(void 0===t?x:t)(v(B-g,0)),c=0;g<B;g++,c++)g in A&&m(a,c,A[g]);return a.length=c,a}})},{"../internals/array-method-has-species-support":65,"../internals/array-slice":68,"../internals/create-property":80,"../internals/export":93,"../internals/global":104,"../internals/is-array":113,"../internals/is-constructor":115,"../internals/is-object":117,"../internals/length-of-array-like":123,"../internals/to-absolute-index":153,"../internals/to-indexed-object":154,"../internals/well-known-symbol":166}],175:[function(u,d,t){"use strict";var a=u("../internals/export"),c=u("../internals/function-uncurry-this"),i=u("../internals/a-callable"),l=u("../internals/to-object"),D=u("../internals/length-of-array-like"),p=u("../internals/to-string"),b=u("../internals/fails"),y=u("../internals/array-sort"),m=u("../internals/array-method-is-strict"),A=u("../internals/engine-ff-version"),E=u("../internals/engine-is-ie-or-edge"),C=u("../internals/engine-v8-version"),g=u("../internals/engine-webkit-version"),h=[],x=c(h.sort),v=c(h.push),B=b((function(){h.sort(void 0)})),w=b((function(){h.sort(null)})),j=m("sort"),k=!b((function(){if(C)return C<70;if(!(A&&A>3)){if(E)return!0;if(g)return g<603;var u,d,t,a,c="";for(u=65;u<76;u++){switch(d=String.fromCharCode(u),u){case 66:case 69:case 70:case 72:t=3;break;case 68:case 71:t=4;break;default:t=2}for(a=0;a<47;a++)h.push({k:d+a,v:t})}for(h.sort((function(u,d){return d.v-u.v})),a=0;a<h.length;a++)d=h[a].k.charAt(0),c.charAt(c.length-1)!==d&&(c+=d);return"DGBEFHACIJK"!==c}}));a({target:"Array",proto:!0,forced:B||!w||!j||!k},{sort:function sort(u){void 0!==u&&i(u);var d=l(this);if(k)return void 0===u?x(d):x(d,u);var t,a,c=[],b=D(d);for(a=0;a<b;a++)a in d&&v(c,d[a]);for(y(c,function(u){return function(d,t){return void 0===t?-1:void 0===d?1:void 0!==u?+u(d,t)||0:p(d)>p(t)?1:-1}}(u)),t=c.length,a=0;a<t;)d[a]=c[a++];for(;a<b;)delete d[a++];return d}})},{"../internals/a-callable":57,"../internals/array-method-is-strict":66,"../internals/array-sort":69,"../internals/engine-ff-version":86,"../internals/engine-is-ie-or-edge":87,"../internals/engine-v8-version":89,"../internals/engine-webkit-version":90,"../internals/export":93,"../internals/fails":94,"../internals/function-uncurry-this":99,"../internals/length-of-array-like":123,"../internals/to-object":157,"../internals/to-string":161}],176:[function(u,d,t){var a=u("../internals/global");u("../internals/set-to-string-tag")(a.JSON,"JSON",!0)},{"../internals/global":104,"../internals/set-to-string-tag":147}],177:[function(u,d,t){},{}],178:[function(u,d,t){u("../internals/export")({target:"Object",stat:!0,sham:!u("../internals/descriptors")},{create:u("../internals/object-create")})},{"../internals/descriptors":83,"../internals/export":93,"../internals/object-create":127}],179:[function(u,d,t){var a=u("../internals/export"),c=u("../internals/descriptors");a({target:"Object",stat:!0,forced:!c,sham:!c},{defineProperty:u("../internals/object-define-property").f})},{"../internals/descriptors":83,"../internals/export":93,"../internals/object-define-property":129}],180:[function(u,d,t){arguments[4][177][0].apply(t,arguments)},{dup:177}],181:[function(u,d,t){var a=u("../internals/export"),c=u("../internals/number-parse-int");a({global:!0,forced:parseInt!=c},{parseInt:c})},{"../internals/export":93,"../internals/number-parse-int":126}],182:[function(u,d,t){arguments[4][177][0].apply(t,arguments)},{dup:177}],183:[function(u,d,t){arguments[4][177][0].apply(t,arguments)},{dup:177}],184:[function(u,d,t){"use strict";var a=u("../internals/string-multibyte").charAt,c=u("../internals/to-string"),i=u("../internals/internal-state"),l=u("../internals/define-iterator"),D="String Iterator",p=i.set,b=i.getterFor(D);l(String,"String",(function(u){p(this,{type:D,string:c(u),index:0})}),(function next(){var u,d=b(this),t=d.string,c=d.index;return c>=t.length?{value:void 0,done:!0}:(u=a(t,c),d.index+=u.length,{value:u,done:!1})}))},{"../internals/define-iterator":81,"../internals/internal-state":111,"../internals/string-multibyte":151,"../internals/to-string":161}],185:[function(u,d,t){u("../internals/define-well-known-symbol")("asyncIterator")},{"../internals/define-well-known-symbol":82}],186:[function(u,d,t){arguments[4][177][0].apply(t,arguments)},{dup:177}],187:[function(u,d,t){u("../internals/define-well-known-symbol")("hasInstance")},{"../internals/define-well-known-symbol":82}],188:[function(u,d,t){u("../internals/define-well-known-symbol")("isConcatSpreadable")},{"../internals/define-well-known-symbol":82}],189:[function(u,d,t){u("../internals/define-well-known-symbol")("iterator")},{"../internals/define-well-known-symbol":82}],190:[function(u,d,t){"use strict";var a=u("../internals/export"),c=u("../internals/global"),i=u("../internals/get-built-in"),l=u("../internals/function-apply"),D=u("../internals/function-call"),p=u("../internals/function-uncurry-this"),b=u("../internals/is-pure"),y=u("../internals/descriptors"),m=u("../internals/native-symbol"),A=u("../internals/fails"),E=u("../internals/has-own-property"),C=u("../internals/is-array"),g=u("../internals/is-callable"),h=u("../internals/is-object"),x=u("../internals/object-is-prototype-of"),v=u("../internals/is-symbol"),B=u("../internals/an-object"),w=u("../internals/to-object"),j=u("../internals/to-indexed-object"),k=u("../internals/to-property-key"),S=u("../internals/to-string"),O=u("../internals/create-property-descriptor"),R=u("../internals/object-create"),_=u("../internals/object-keys"),T=u("../internals/object-get-own-property-names"),I=u("../internals/object-get-own-property-names-external"),P=u("../internals/object-get-own-property-symbols"),X=u("../internals/object-get-own-property-descriptor"),L=u("../internals/object-define-property"),N=u("../internals/object-property-is-enumerable"),M=u("../internals/array-slice"),U=u("../internals/redefine"),G=u("../internals/shared"),q=u("../internals/shared-key"),z=u("../internals/hidden-keys"),$=u("../internals/uid"),H=u("../internals/well-known-symbol"),Z=u("../internals/well-known-symbol-wrapped"),Y=u("../internals/define-well-known-symbol"),V=u("../internals/set-to-string-tag"),W=u("../internals/internal-state"),J=u("../internals/array-iteration").forEach,K=q("hidden"),Q="Symbol",uu=H("toPrimitive"),eu=W.set,du=W.getterFor(Q),nu=Object.prototype,tu=c.Symbol,ru=tu&&tu.prototype,au=c.TypeError,cu=c.QObject,ou=i("JSON","stringify"),iu=X.f,su=L.f,fu=I.f,lu=N.f,Du=p([].push),pu=G("symbols"),bu=G("op-symbols"),yu=G("string-to-symbol-registry"),Fu=G("symbol-to-string-registry"),mu=G("wks"),Au=!cu||!cu.prototype||!cu.prototype.findChild,Eu=y&&A((function(){return 7!=R(su({},"a",{get:function(){return su(this,"a",{value:7}).a}})).a}))?function(u,d,t){var a=iu(nu,d);a&&delete nu[d],su(u,d,t),a&&u!==nu&&su(nu,d,a)}:su,wrap=function(u,d){var t=pu[u]=R(ru);return eu(t,{type:Q,tag:u,description:d}),y||(t.description=d),t},Cu=function defineProperty(u,d,t){u===nu&&Cu(bu,d,t),B(u);var a=k(d);return B(t),E(pu,a)?(t.enumerable?(E(u,K)&&u[K][a]&&(u[K][a]=!1),t=R(t,{enumerable:O(0,!1)})):(E(u,K)||su(u,K,O(1,{})),u[K][a]=!0),Eu(u,a,t)):su(u,a,t)},gu=function defineProperties(u,d){B(u);var t=j(d),a=_(t).concat(Bu(t));return J(a,(function(d){y&&!D(hu,t,d)||Cu(u,d,t[d])})),u},hu=function propertyIsEnumerable(u){var d=k(u),t=D(lu,this,d);return!(this===nu&&E(pu,d)&&!E(bu,d))&&(!(t||!E(this,d)||!E(pu,d)||E(this,K)&&this[K][d])||t)},xu=function getOwnPropertyDescriptor(u,d){var t=j(u),a=k(d);if(t!==nu||!E(pu,a)||E(bu,a)){var c=iu(t,a);return!c||!E(pu,a)||E(t,K)&&t[K][a]||(c.enumerable=!0),c}},vu=function getOwnPropertyNames(u){var d=fu(j(u)),t=[];return J(d,(function(u){E(pu,u)||E(z,u)||Du(t,u)})),t},Bu=function getOwnPropertySymbols(u){var d=u===nu,t=fu(d?bu:j(u)),a=[];return J(t,(function(u){!E(pu,u)||d&&!E(nu,u)||Du(a,pu[u])})),a};(m||(tu=function Symbol(){if(x(ru,this))throw au("Symbol is not a constructor");var u=arguments.length&&void 0!==arguments[0]?S(arguments[0]):void 0,d=$(u),setter=function(u){this===nu&&D(setter,bu,u),E(this,K)&&E(this[K],d)&&(this[K][d]=!1),Eu(this,d,O(1,u))};return y&&Au&&Eu(nu,d,{configurable:!0,set:setter}),wrap(d,u)},U(ru=tu.prototype,"toString",(function toString(){return du(this).tag})),U(tu,"withoutSetter",(function(u){return wrap($(u),u)})),N.f=hu,L.f=Cu,X.f=xu,T.f=I.f=vu,P.f=Bu,Z.f=function(u){return wrap(H(u),u)},y&&(su(ru,"description",{configurable:!0,get:function description(){return du(this).description}}),b||U(nu,"propertyIsEnumerable",hu,{unsafe:!0}))),a({global:!0,wrap:!0,forced:!m,sham:!m},{Symbol:tu}),J(_(mu),(function(u){Y(u)})),a({target:Q,stat:!0,forced:!m},{for:function(u){var d=S(u);if(E(yu,d))return yu[d];var t=tu(d);return yu[d]=t,Fu[t]=d,t},keyFor:function keyFor(u){if(!v(u))throw au(u+" is not a symbol");if(E(Fu,u))return Fu[u]},useSetter:function(){Au=!0},useSimple:function(){Au=!1}}),a({target:"Object",stat:!0,forced:!m,sham:!y},{create:function create(u,d){return void 0===d?R(u):gu(R(u),d)},defineProperty:Cu,defineProperties:gu,getOwnPropertyDescriptor:xu}),a({target:"Object",stat:!0,forced:!m},{getOwnPropertyNames:vu,getOwnPropertySymbols:Bu}),a({target:"Object",stat:!0,forced:A((function(){P.f(1)}))},{getOwnPropertySymbols:function getOwnPropertySymbols(u){return P.f(w(u))}}),ou)&&a({target:"JSON",stat:!0,forced:!m||A((function(){var u=tu();return"[null]"!=ou([u])||"{}"!=ou({a:u})||"{}"!=ou(Object(u))}))},{stringify:function stringify(u,d,t){var a=M(arguments),c=d;if((h(d)||void 0!==u)&&!v(u))return C(d)||(d=function(u,d){if(g(c)&&(d=D(c,this,u,d)),!v(d))return d}),a[1]=d,l(ou,null,a)}});if(!ru[uu]){var wu=ru.valueOf;U(ru,uu,(function(u){return D(wu,this)}))}V(tu,Q),z[K]=!0},{"../internals/an-object":60,"../internals/array-iteration":64,"../internals/array-slice":68,"../internals/create-property-descriptor":79,"../internals/define-well-known-symbol":82,"../internals/descriptors":83,"../internals/export":93,"../internals/fails":94,"../internals/function-apply":95,"../internals/function-call":97,"../internals/function-uncurry-this":99,"../internals/get-built-in":100,"../internals/global":104,"../internals/has-own-property":105,"../internals/hidden-keys":106,"../internals/internal-state":111,"../internals/is-array":113,"../internals/is-callable":114,"../internals/is-object":117,"../internals/is-pure":118,"../internals/is-symbol":119,"../internals/native-symbol":124,"../internals/object-create":127,"../internals/object-define-property":129,"../internals/object-get-own-property-descriptor":130,"../internals/object-get-own-property-names":132,"../internals/object-get-own-property-names-external":131,"../internals/object-get-own-property-symbols":133,"../internals/object-is-prototype-of":135,"../internals/object-keys":137,"../internals/object-property-is-enumerable":138,"../internals/redefine":143,"../internals/set-to-string-tag":147,"../internals/shared":150,"../internals/shared-key":148,"../internals/to-indexed-object":154,"../internals/to-object":157,"../internals/to-property-key":159,"../internals/to-string":161,"../internals/uid":163,"../internals/well-known-symbol":166,"../internals/well-known-symbol-wrapped":165}],191:[function(u,d,t){u("../internals/define-well-known-symbol")("matchAll")},{"../internals/define-well-known-symbol":82}],192:[function(u,d,t){u("../internals/define-well-known-symbol")("match")},{"../internals/define-well-known-symbol":82}],193:[function(u,d,t){u("../internals/define-well-known-symbol")("replace")},{"../internals/define-well-known-symbol":82}],194:[function(u,d,t){u("../internals/define-well-known-symbol")("search")},{"../internals/define-well-known-symbol":82}],195:[function(u,d,t){u("../internals/define-well-known-symbol")("species")},{"../internals/define-well-known-symbol":82}],196:[function(u,d,t){u("../internals/define-well-known-symbol")("split")},{"../internals/define-well-known-symbol":82}],197:[function(u,d,t){u("../internals/define-well-known-symbol")("toPrimitive")},{"../internals/define-well-known-symbol":82}],198:[function(u,d,t){u("../internals/define-well-known-symbol")("toStringTag")},{"../internals/define-well-known-symbol":82}],199:[function(u,d,t){u("../internals/define-well-known-symbol")("unscopables")},{"../internals/define-well-known-symbol":82}],200:[function(u,d,t){u("../internals/define-well-known-symbol")("asyncDispose")},{"../internals/define-well-known-symbol":82}],201:[function(u,d,t){u("../internals/define-well-known-symbol")("dispose")},{"../internals/define-well-known-symbol":82}],202:[function(u,d,t){u("../internals/define-well-known-symbol")("matcher")},{"../internals/define-well-known-symbol":82}],203:[function(u,d,t){u("../internals/define-well-known-symbol")("metadata")},{"../internals/define-well-known-symbol":82}],204:[function(u,d,t){u("../internals/define-well-known-symbol")("observable")},{"../internals/define-well-known-symbol":82}],205:[function(u,d,t){u("../internals/define-well-known-symbol")("patternMatch")},{"../internals/define-well-known-symbol":82}],206:[function(u,d,t){u("../internals/define-well-known-symbol")("replaceAll")},{"../internals/define-well-known-symbol":82}],207:[function(u,d,t){u("../modules/es.array.iterator");var a=u("../internals/dom-iterables"),c=u("../internals/global"),i=u("../internals/classof"),l=u("../internals/create-non-enumerable-property"),D=u("../internals/iterators"),p=u("../internals/well-known-symbol")("toStringTag");for(var b in a){var y=c[b],m=y&&y.prototype;m&&i(m)!==p&&l(m,p,b),D[b]=D.Array}},{"../internals/classof":75,"../internals/create-non-enumerable-property":78,"../internals/dom-iterables":85,"../internals/global":104,"../internals/iterators":122,"../internals/well-known-symbol":166,"../modules/es.array.iterator":173}],208:[function(u,d,t){var a=u("../../es/array/from");d.exports=a},{"../../es/array/from":34}],209:[function(u,d,t){var a=u("../../es/array/is-array");d.exports=a},{"../../es/array/is-array":35}],210:[function(u,d,t){var a=u("../../../es/array/virtual/for-each");d.exports=a},{"../../../es/array/virtual/for-each":37}],211:[function(u,d,t){var a=u("../es/get-iterator-method");u("../modules/web.dom-collections.iterator"),d.exports=a},{"../es/get-iterator-method":41,"../modules/web.dom-collections.iterator":207}],212:[function(u,d,t){var a=u("../../es/instance/concat");d.exports=a},{"../../es/instance/concat":42}],213:[function(u,d,t){var a=u("../../es/instance/flags");d.exports=a},{"../../es/instance/flags":43}],214:[function(u,d,t){u("../../modules/web.dom-collections.iterator");var a=u("../../internals/classof"),c=u("../../internals/has-own-property"),i=u("../../internals/object-is-prototype-of"),l=u("../array/virtual/for-each"),D=Array.prototype,p={DOMTokenList:!0,NodeList:!0};d.exports=function(u){var d=u.forEach;return u===D||i(D,u)&&d===D.forEach||c(p,a(u))?l:d}},{"../../internals/classof":75,"../../internals/has-own-property":105,"../../internals/object-is-prototype-of":135,"../../modules/web.dom-collections.iterator":207,"../array/virtual/for-each":210}],215:[function(u,d,t){var a=u("../../es/instance/index-of");d.exports=a},{"../../es/instance/index-of":44}],216:[function(u,d,t){var a=u("../../es/instance/slice");d.exports=a},{"../../es/instance/slice":45}],217:[function(u,d,t){var a=u("../../es/instance/sort");d.exports=a},{"../../es/instance/sort":46}],218:[function(u,d,t){var a=u("../../es/object/create");d.exports=a},{"../../es/object/create":47}],219:[function(u,d,t){var a=u("../../es/object/define-property");d.exports=a},{"../../es/object/define-property":48}],220:[function(u,d,t){var a=u("../es/parse-int");d.exports=a},{"../es/parse-int":49}],221:[function(u,d,t){var a=u("../../es/symbol");u("../../modules/web.dom-collections.iterator"),d.exports=a},{"../../es/symbol":51,"../../modules/web.dom-collections.iterator":207}],222:[function(u,d,t){d.exports=[{name:"C",alias:"Other",isBmpLast:!0,bmp:"\0--­͸͹΀-΃΋΍΢԰՗՘֋֌֐׈-׏׫-׮׵-؅؜۝܎܏݋݌޲-޿߻߼࠮࠯࠿࡜࡝࡟࡫-࡯࢏-ࢗ࣢঄঍঎঑঒঩঱঳-঵঺঻৅৆৉৊৏-৖৘-৛৞৤৥৿਀਄਋-਎਑਒਩਱਴਷਺਻਽੃-੆੉੊੎-੐੒-੘੝੟-੥੷-઀઄઎઒઩઱઴઺઻૆૊૎૏૑-૟૤૥૲-૸଀଄଍଎଑଒଩଱଴଺଻୅୆୉୊୎-୔୘-୛୞୤୥୸-஁஄஋-஍஑஖-஘஛஝஠-஢஥-஧஫-஭஺-஽௃-௅௉௎௏௑-௖௘-௥௻-௿఍఑఩఺఻౅౉౎-౔౗౛౜౞౟౤౥౰-౶಍಑಩಴಺಻೅೉೎-೔೗-೜೟೤೥೰ೳ-೿഍഑൅൉൐-൓൤൥඀඄඗-඙඲඼඾඿෇-෉෋-෎෕෗෠-෥෰෱෵-฀฻-฾๜-຀຃຅຋຤຦຾຿໅໇໎໏໚໛໠-໿཈཭-཰྘྽࿍࿛-࿿჆჈-჌჎჏቉቎቏቗቙቞቟኉኎኏኱኶኷኿዁዆዇዗጑጖጗፛፜፽-፿᎚-᎟᏶᏷᏾᏿᚝-᚟᛹-᛿᜖-᜞᜷-᜿᝔-᝟᝭᝱᝴-᝿៞៟៪-៯៺-៿᠎᠚-᠟᡹-᡿᢫-᢯᣶-᣿᤟᤬-᤯᤼-᤿᥁-᥃᥮᥯᥵-᥿᦬-᦯᧊-᧏᧛-᧝᨜᨝᩟᩽᩾᪊-᪏᪚-᪟᪮᪯᫏-᫿᭍-᭏᭿᯴-᯻᰸-᰺᱊-᱌Ᲊ-᲏᲻᲼᳈-᳏᳻-᳿἖἗἞἟὆὇὎὏὘὚὜὞὾὿᾵῅῔῕῜῰῱῵῿​-‏‪-‮⁠-⁯⁲⁳₏₝-₟⃁-⃏⃱-⃿↌-↏␧-␿⑋-⑟⭴⭵⮖⳴-⳸⴦⴨-⴬⴮⴯⵨-⵮⵱-⵾⶗-⶟⶧⶯⶷⶿⷇⷏⷗⷟⹞-⹿⺚⻴-⻿⿖-⿯⿼-⿿぀゗゘㄀-㄄㄰㆏㇤-㇯㈟꒍-꒏꓇-꓏꘬-꘿꛸-꛿Ɤ-꟏꟒꟔Ꟛ-꟱꠭-꠯꠺-꠿꡸-꡿꣆-꣍꣚-꣟꥔-꥞꥽-꥿꧎꧚-꧝꧿꨷-꨿꩎꩏꩚꩛꫃-꫚꫷-꬀꬇꬈꬏꬐꬗-꬟꬧꬯꭬-꭯꯮꯯꯺-꯿힤-힯퟇-퟊퟼-﩮﩯﫚-﫿﬇-﬒﬘-﬜﬷﬽﬿﭂﭅﯃-﯒﶐﶑﷈-﷎﷐-﷯︚-︟﹓﹧﹬-﹯﹵﻽-＀﾿-￁￈￉￐￑￘￙￝-￟￧￯-￻￾￿",astral:"\ud800[\udc0c\udc27\udc3b\udc3e\udc4e\udc4f\udc5e-\udc7f\udcfb-\udcff\udd03-\udd06\udd34-\udd36\udd8f\udd9d-\udd9f\udda1-\uddcf\uddfe-\ude7f\ude9d-\ude9f\uded1-\udedf\udefc-\udeff\udf24-\udf2c\udf4b-\udf4f\udf7b-\udf7f\udf9e\udfc4-\udfc7\udfd6-\udfff]|\ud801[\udc9e\udc9f\udcaa-\udcaf\udcd4-\udcd7\udcfc-\udcff\udd28-\udd2f\udd64-\udd6e\udd7b\udd8b\udd93\udd96\udda2\uddb2\uddba\uddbd-\uddff\udf37-\udf3f\udf56-\udf5f\udf68-\udf7f\udf86\udfb1\udfbb-\udfff]|\ud802[\udc06\udc07\udc09\udc36\udc39-\udc3b\udc3d\udc3e\udc56\udc9f-\udca6\udcb0-\udcdf\udcf3\udcf6-\udcfa\udd1c-\udd1e\udd3a-\udd3e\udd40-\udd7f\uddb8-\uddbb\uddd0\uddd1\ude04\ude07-\ude0b\ude14\ude18\ude36\ude37\ude3b-\ude3e\ude49-\ude4f\ude59-\ude5f\udea0-\udebf\udee7-\udeea\udef7-\udeff\udf36-\udf38\udf56\udf57\udf73-\udf77\udf92-\udf98\udf9d-\udfa8\udfb0-\udfff]|\ud803[\udc49-\udc7f\udcb3-\udcbf\udcf3-\udcf9\udd28-\udd2f\udd3a-\ude5f\ude7f\udeaa\udeae\udeaf\udeb2-\udeff\udf28-\udf2f\udf5a-\udf6f\udf8a-\udfaf\udfcc-\udfdf\udff7-\udfff]|\ud804[\udc4e-\udc51\udc76-\udc7e\udcbd\udcc3-\udccf\udce9-\udcef\udcfa-\udcff\udd35\udd48-\udd4f\udd77-\udd7f\udde0\uddf5-\uddff\ude12\ude3f-\ude7f\ude87\ude89\ude8e\ude9e\udeaa-\udeaf\udeeb-\udeef\udefa-\udeff\udf04\udf0d\udf0e\udf11\udf12\udf29\udf31\udf34\udf3a\udf45\udf46\udf49\udf4a\udf4e\udf4f\udf51-\udf56\udf58-\udf5c\udf64\udf65\udf6d-\udf6f\udf75-\udfff]|\ud805[\udc5c\udc62-\udc7f\udcc8-\udccf\udcda-\udd7f\uddb6\uddb7\uddde-\uddff\ude45-\ude4f\ude5a-\ude5f\ude6d-\ude7f\udeba-\udebf\udeca-\udeff\udf1b\udf1c\udf2c-\udf2f\udf47-\udfff]|\ud806[\udc3c-\udc9f\udcf3-\udcfe\udd07\udd08\udd0a\udd0b\udd14\udd17\udd36\udd39\udd3a\udd47-\udd4f\udd5a-\udd9f\udda8\udda9\uddd8\uddd9\udde5-\uddff\ude48-\ude4f\udea3-\udeaf\udef9-\udfff]|\ud807[\udc09\udc37\udc46-\udc4f\udc6d-\udc6f\udc90\udc91\udca8\udcb7-\udcff\udd07\udd0a\udd37-\udd39\udd3b\udd3e\udd48-\udd4f\udd5a-\udd5f\udd66\udd69\udd8f\udd92\udd99-\udd9f\uddaa-\udedf\udef9-\udfaf\udfb1-\udfbf\udff2-\udffe]|\ud808[\udf9a-\udfff]|\ud809[\udc6f\udc75-\udc7f\udd44-\udfff]|[\ud80a\ud80e-\ud810\ud812-\ud819\ud824-\ud82a\ud82d\ud82e\ud830-\ud832\ud83f\ud87b-\ud87d\ud87f\ud885-\udb3f\udb41-\udbff][\udc00-\udfff]|\ud80b[\udc00-\udf8f\udff3-\udfff]|\ud80d[\udc2f-\udfff]|\ud811[\ude47-\udfff]|\ud81a[\ude39-\ude3f\ude5f\ude6a-\ude6d\udebf\udeca-\udecf\udeee\udeef\udef6-\udeff\udf46-\udf4f\udf5a\udf62\udf78-\udf7c\udf90-\udfff]|\ud81b[\udc00-\ude3f\ude9b-\udeff\udf4b-\udf4e\udf88-\udf8e\udfa0-\udfdf\udfe5-\udfef\udff2-\udfff]|\ud821[\udff8-\udfff]|\ud823[\udcd6-\udcff\udd09-\udfff]|\ud82b[\udc00-\udfef\udff4\udffc\udfff]|\ud82c[\udd23-\udd4f\udd53-\udd63\udd68-\udd6f\udefc-\udfff]|\ud82f[\udc6b-\udc6f\udc7d-\udc7f\udc89-\udc8f\udc9a\udc9b\udca0-\udfff]|\ud833[\udc00-\udeff\udf2e\udf2f\udf47-\udf4f\udfc4-\udfff]|\ud834[\udcf6-\udcff\udd27\udd28\udd73-\udd7a\uddeb-\uddff\ude46-\udedf\udef4-\udeff\udf57-\udf5f\udf79-\udfff]|\ud835[\udc55\udc9d\udca0\udca1\udca3\udca4\udca7\udca8\udcad\udcba\udcbc\udcc4\udd06\udd0b\udd0c\udd15\udd1d\udd3a\udd3f\udd45\udd47-\udd49\udd51\udea6\udea7\udfcc\udfcd]|\ud836[\ude8c-\ude9a\udea0\udeb0-\udfff]|\ud837[\udc00-\udeff\udf1f-\udfff]|\ud838[\udc07\udc19\udc1a\udc22\udc25\udc2b-\udcff\udd2d-\udd2f\udd3e\udd3f\udd4a-\udd4d\udd50-\ude8f\udeaf-\udebf\udefa-\udefe\udf00-\udfff]|\ud839[\udc00-\udfdf\udfe7\udfec\udfef\udfff]|\ud83a[\udcc5\udcc6\udcd7-\udcff\udd4c-\udd4f\udd5a-\udd5d\udd60-\udfff]|\ud83b[\udc00-\udc70\udcb5-\udd00\udd3e-\uddff\ude04\ude20\ude23\ude25\ude26\ude28\ude33\ude38\ude3a\ude3c-\ude41\ude43-\ude46\ude48\ude4a\ude4c\ude50\ude53\ude55\ude56\ude58\ude5a\ude5c\ude5e\ude60\ude63\ude65\ude66\ude6b\ude73\ude78\ude7d\ude7f\ude8a\ude9c-\udea0\udea4\udeaa\udebc-\udeef\udef2-\udfff]|\ud83c[\udc2c-\udc2f\udc94-\udc9f\udcaf\udcb0\udcc0\udcd0\udcf6-\udcff\uddae-\udde5\ude03-\ude0f\ude3c-\ude3f\ude49-\ude4f\ude52-\ude5f\ude66-\udeff]|\ud83d[\uded8-\udedc\udeed-\udeef\udefd-\udeff\udf74-\udf7f\udfd9-\udfdf\udfec-\udfef\udff1-\udfff]|\ud83e[\udc0c-\udc0f\udc48-\udc4f\udc5a-\udc5f\udc88-\udc8f\udcae\udcaf\udcb2-\udcff\ude54-\ude5f\ude6e\ude6f\ude75-\ude77\ude7d-\ude7f\ude87-\ude8f\udead-\udeaf\udebb-\udebf\udec6-\udecf\udeda-\udedf\udee8-\udeef\udef7-\udeff\udf93\udfcb-\udfef\udffa-\udfff]|\ud869[\udee0-\udeff]|\ud86d[\udf39-\udf3f]|\ud86e[\udc1e\udc1f]|\ud873[\udea2-\udeaf]|\ud87a[\udfe1-\udfff]|\ud87e[\ude1e-\udfff]|\ud884[\udf4b-\udfff]|\udb40[\udc00-\udcff\uddf0-\udfff]"},{name:"Cc",alias:"Control",bmp:"\0--"},{name:"Cf",alias:"Format",bmp:"­؀-؅؜۝܏࢐࢑࣢᠎​-‏‪-‮⁠-⁤⁦-⁯\ufeff￹-￻",astral:"\ud804[\udcbd\udccd]|\ud80d[\udc30-\udc38]|\ud82f[\udca0-\udca3]|\ud834[\udd73-\udd7a]|\udb40[\udc01\udc20-\udc7f]"},{name:"Cn",alias:"Unassigned",bmp:"͸͹΀-΃΋΍΢԰՗՘֋֌֐׈-׏׫-׮׵-׿܎݋݌޲-޿߻߼࠮࠯࠿࡜࡝࡟࡫-࡯࢏࢒-ࢗ঄঍঎঑঒঩঱঳-঵঺঻৅৆৉৊৏-৖৘-৛৞৤৥৿਀਄਋-਎਑਒਩਱਴਷਺਻਽੃-੆੉੊੎-੐੒-੘੝੟-੥੷-઀઄઎઒઩઱઴઺઻૆૊૎૏૑-૟૤૥૲-૸଀଄଍଎଑଒଩଱଴଺଻୅୆୉୊୎-୔୘-୛୞୤୥୸-஁஄஋-஍஑஖-஘஛஝஠-஢஥-஧஫-஭஺-஽௃-௅௉௎௏௑-௖௘-௥௻-௿఍఑఩఺఻౅౉౎-౔౗౛౜౞౟౤౥౰-౶಍಑಩಴಺಻೅೉೎-೔೗-೜೟೤೥೰ೳ-೿഍഑൅൉൐-൓൤൥඀඄඗-඙඲඼඾඿෇-෉෋-෎෕෗෠-෥෰෱෵-฀฻-฾๜-຀຃຅຋຤຦຾຿໅໇໎໏໚໛໠-໿཈཭-཰྘྽࿍࿛-࿿჆჈-჌჎჏቉቎቏቗቙቞቟኉኎኏኱኶኷኿዁዆዇዗጑጖጗፛፜፽-፿᎚-᎟᏶᏷᏾᏿᚝-᚟᛹-᛿᜖-᜞᜷-᜿᝔-᝟᝭᝱᝴-᝿៞៟៪-៯៺-៿᠚-᠟᡹-᡿᢫-᢯᣶-᣿᤟᤬-᤯᤼-᤿᥁-᥃᥮᥯᥵-᥿᦬-᦯᧊-᧏᧛-᧝᨜᨝᩟᩽᩾᪊-᪏᪚-᪟᪮᪯᫏-᫿᭍-᭏᭿᯴-᯻᰸-᰺᱊-᱌Ᲊ-᲏᲻᲼᳈-᳏᳻-᳿἖἗἞἟὆὇὎὏὘὚὜὞὾὿᾵῅῔῕῜῰῱῵῿⁥⁲⁳₏₝-₟⃁-⃏⃱-⃿↌-↏␧-␿⑋-⑟⭴⭵⮖⳴-⳸⴦⴨-⴬⴮⴯⵨-⵮⵱-⵾⶗-⶟⶧⶯⶷⶿⷇⷏⷗⷟⹞-⹿⺚⻴-⻿⿖-⿯⿼-⿿぀゗゘㄀-㄄㄰㆏㇤-㇯㈟꒍-꒏꓇-꓏꘬-꘿꛸-꛿Ɤ-꟏꟒꟔Ꟛ-꟱꠭-꠯꠺-꠿꡸-꡿꣆-꣍꣚-꣟꥔-꥞꥽-꥿꧎꧚-꧝꧿꨷-꨿꩎꩏꩚꩛꫃-꫚꫷-꬀꬇꬈꬏꬐꬗-꬟꬧꬯꭬-꭯꯮꯯꯺-꯿힤-힯퟇-퟊퟼-퟿﩮﩯﫚-﫿﬇-﬒﬘-﬜﬷﬽﬿﭂﭅﯃-﯒﶐﶑﷈-﷎﷐-﷯︚-︟﹓﹧﹬-﹯﹵﻽﻾＀﾿-￁￈￉￐￑￘￙￝-￟￧￯-￸￾￿",astral:"\ud800[\udc0c\udc27\udc3b\udc3e\udc4e\udc4f\udc5e-\udc7f\udcfb-\udcff\udd03-\udd06\udd34-\udd36\udd8f\udd9d-\udd9f\udda1-\uddcf\uddfe-\ude7f\ude9d-\ude9f\uded1-\udedf\udefc-\udeff\udf24-\udf2c\udf4b-\udf4f\udf7b-\udf7f\udf9e\udfc4-\udfc7\udfd6-\udfff]|\ud801[\udc9e\udc9f\udcaa-\udcaf\udcd4-\udcd7\udcfc-\udcff\udd28-\udd2f\udd64-\udd6e\udd7b\udd8b\udd93\udd96\udda2\uddb2\uddba\uddbd-\uddff\udf37-\udf3f\udf56-\udf5f\udf68-\udf7f\udf86\udfb1\udfbb-\udfff]|\ud802[\udc06\udc07\udc09\udc36\udc39-\udc3b\udc3d\udc3e\udc56\udc9f-\udca6\udcb0-\udcdf\udcf3\udcf6-\udcfa\udd1c-\udd1e\udd3a-\udd3e\udd40-\udd7f\uddb8-\uddbb\uddd0\uddd1\ude04\ude07-\ude0b\ude14\ude18\ude36\ude37\ude3b-\ude3e\ude49-\ude4f\ude59-\ude5f\udea0-\udebf\udee7-\udeea\udef7-\udeff\udf36-\udf38\udf56\udf57\udf73-\udf77\udf92-\udf98\udf9d-\udfa8\udfb0-\udfff]|\ud803[\udc49-\udc7f\udcb3-\udcbf\udcf3-\udcf9\udd28-\udd2f\udd3a-\ude5f\ude7f\udeaa\udeae\udeaf\udeb2-\udeff\udf28-\udf2f\udf5a-\udf6f\udf8a-\udfaf\udfcc-\udfdf\udff7-\udfff]|\ud804[\udc4e-\udc51\udc76-\udc7e\udcc3-\udccc\udcce\udccf\udce9-\udcef\udcfa-\udcff\udd35\udd48-\udd4f\udd77-\udd7f\udde0\uddf5-\uddff\ude12\ude3f-\ude7f\ude87\ude89\ude8e\ude9e\udeaa-\udeaf\udeeb-\udeef\udefa-\udeff\udf04\udf0d\udf0e\udf11\udf12\udf29\udf31\udf34\udf3a\udf45\udf46\udf49\udf4a\udf4e\udf4f\udf51-\udf56\udf58-\udf5c\udf64\udf65\udf6d-\udf6f\udf75-\udfff]|\ud805[\udc5c\udc62-\udc7f\udcc8-\udccf\udcda-\udd7f\uddb6\uddb7\uddde-\uddff\ude45-\ude4f\ude5a-\ude5f\ude6d-\ude7f\udeba-\udebf\udeca-\udeff\udf1b\udf1c\udf2c-\udf2f\udf47-\udfff]|\ud806[\udc3c-\udc9f\udcf3-\udcfe\udd07\udd08\udd0a\udd0b\udd14\udd17\udd36\udd39\udd3a\udd47-\udd4f\udd5a-\udd9f\udda8\udda9\uddd8\uddd9\udde5-\uddff\ude48-\ude4f\udea3-\udeaf\udef9-\udfff]|\ud807[\udc09\udc37\udc46-\udc4f\udc6d-\udc6f\udc90\udc91\udca8\udcb7-\udcff\udd07\udd0a\udd37-\udd39\udd3b\udd3e\udd48-\udd4f\udd5a-\udd5f\udd66\udd69\udd8f\udd92\udd99-\udd9f\uddaa-\udedf\udef9-\udfaf\udfb1-\udfbf\udff2-\udffe]|\ud808[\udf9a-\udfff]|\ud809[\udc6f\udc75-\udc7f\udd44-\udfff]|[\ud80a\ud80e-\ud810\ud812-\ud819\ud824-\ud82a\ud82d\ud82e\ud830-\ud832\ud83f\ud87b-\ud87d\ud87f\ud885-\udb3f\udb41-\udb7f][\udc00-\udfff]|\ud80b[\udc00-\udf8f\udff3-\udfff]|\ud80d[\udc2f\udc39-\udfff]|\ud811[\ude47-\udfff]|\ud81a[\ude39-\ude3f\ude5f\ude6a-\ude6d\udebf\udeca-\udecf\udeee\udeef\udef6-\udeff\udf46-\udf4f\udf5a\udf62\udf78-\udf7c\udf90-\udfff]|\ud81b[\udc00-\ude3f\ude9b-\udeff\udf4b-\udf4e\udf88-\udf8e\udfa0-\udfdf\udfe5-\udfef\udff2-\udfff]|\ud821[\udff8-\udfff]|\ud823[\udcd6-\udcff\udd09-\udfff]|\ud82b[\udc00-\udfef\udff4\udffc\udfff]|\ud82c[\udd23-\udd4f\udd53-\udd63\udd68-\udd6f\udefc-\udfff]|\ud82f[\udc6b-\udc6f\udc7d-\udc7f\udc89-\udc8f\udc9a\udc9b\udca4-\udfff]|\ud833[\udc00-\udeff\udf2e\udf2f\udf47-\udf4f\udfc4-\udfff]|\ud834[\udcf6-\udcff\udd27\udd28\uddeb-\uddff\ude46-\udedf\udef4-\udeff\udf57-\udf5f\udf79-\udfff]|\ud835[\udc55\udc9d\udca0\udca1\udca3\udca4\udca7\udca8\udcad\udcba\udcbc\udcc4\udd06\udd0b\udd0c\udd15\udd1d\udd3a\udd3f\udd45\udd47-\udd49\udd51\udea6\udea7\udfcc\udfcd]|\ud836[\ude8c-\ude9a\udea0\udeb0-\udfff]|\ud837[\udc00-\udeff\udf1f-\udfff]|\ud838[\udc07\udc19\udc1a\udc22\udc25\udc2b-\udcff\udd2d-\udd2f\udd3e\udd3f\udd4a-\udd4d\udd50-\ude8f\udeaf-\udebf\udefa-\udefe\udf00-\udfff]|\ud839[\udc00-\udfdf\udfe7\udfec\udfef\udfff]|\ud83a[\udcc5\udcc6\udcd7-\udcff\udd4c-\udd4f\udd5a-\udd5d\udd60-\udfff]|\ud83b[\udc00-\udc70\udcb5-\udd00\udd3e-\uddff\ude04\ude20\ude23\ude25\ude26\ude28\ude33\ude38\ude3a\ude3c-\ude41\ude43-\ude46\ude48\ude4a\ude4c\ude50\ude53\ude55\ude56\ude58\ude5a\ude5c\ude5e\ude60\ude63\ude65\ude66\ude6b\ude73\ude78\ude7d\ude7f\ude8a\ude9c-\udea0\udea4\udeaa\udebc-\udeef\udef2-\udfff]|\ud83c[\udc2c-\udc2f\udc94-\udc9f\udcaf\udcb0\udcc0\udcd0\udcf6-\udcff\uddae-\udde5\ude03-\ude0f\ude3c-\ude3f\ude49-\ude4f\ude52-\ude5f\ude66-\udeff]|\ud83d[\uded8-\udedc\udeed-\udeef\udefd-\udeff\udf74-\udf7f\udfd9-\udfdf\udfec-\udfef\udff1-\udfff]|\ud83e[\udc0c-\udc0f\udc48-\udc4f\udc5a-\udc5f\udc88-\udc8f\udcae\udcaf\udcb2-\udcff\ude54-\ude5f\ude6e\ude6f\ude75-\ude77\ude7d-\ude7f\ude87-\ude8f\udead-\udeaf\udebb-\udebf\udec6-\udecf\udeda-\udedf\udee8-\udeef\udef7-\udeff\udf93\udfcb-\udfef\udffa-\udfff]|\ud869[\udee0-\udeff]|\ud86d[\udf39-\udf3f]|\ud86e[\udc1e\udc1f]|\ud873[\udea2-\udeaf]|\ud87a[\udfe1-\udfff]|\ud87e[\ude1e-\udfff]|\ud884[\udf4b-\udfff]|\udb40[\udc00\udc02-\udc1f\udc80-\udcff\uddf0-\udfff]|[\udbbf\udbff][\udffe\udfff]"},{name:"Co",alias:"Private_Use",bmp:"-",astral:"[\udb80-\udbbe\udbc0-\udbfe][\udc00-\udfff]|[\udbbf\udbff][\udc00-\udffd]"},{name:"Cs",alias:"Surrogate",bmp:"\ud800-\udfff"},{name:"L",alias:"Letter",bmp:"A-Za-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛱ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢄᢇ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎↃↄⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⸯ々〆〱-〵〻〼ぁ-ゖゝ-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛥꜗ-ꜟꜢ-ꞈꞋ-ꟊꟐꟑꟓꟕ-ꟙꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",astral:"\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf40\udf42-\udf49\udf50-\udf75\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf]|\ud801[\udc00-\udc9d\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\udd70-\udd7a\udd7c-\udd8a\udd8c-\udd92\udd94\udd95\udd97-\udda1\udda3-\uddb1\uddb3-\uddb9\uddbb\uddbc\ude00-\udf36\udf40-\udf55\udf60-\udf67\udf80-\udf85\udf87-\udfb0\udfb2-\udfba]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe\uddbf\ude00\ude10-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd23\ude80-\udea9\udeb0\udeb1\udf00-\udf1c\udf27\udf30-\udf45\udf70-\udf81\udfb0-\udfc4\udfe0-\udff6]|\ud804[\udc03-\udc37\udc71\udc72\udc75\udc83-\udcaf\udcd0-\udce8\udd03-\udd26\udd44\udd47\udd50-\udd72\udd76\udd83-\uddb2\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude2b\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udede\udf05-\udf0c\udf0f\udf10\udf13-\udf28\udf2a-\udf30\udf32\udf33\udf35-\udf39\udf3d\udf50\udf5d-\udf61]|\ud805[\udc00-\udc34\udc47-\udc4a\udc5f-\udc61\udc80-\udcaf\udcc4\udcc5\udcc7\udd80-\uddae\uddd8-\udddb\ude00-\ude2f\ude44\ude80-\udeaa\udeb8\udf00-\udf1a\udf40-\udf46]|\ud806[\udc00-\udc2b\udca0-\udcdf\udcff-\udd06\udd09\udd0c-\udd13\udd15\udd16\udd18-\udd2f\udd3f\udd41\udda0-\udda7\uddaa-\uddd0\udde1\udde3\ude00\ude0b-\ude32\ude3a\ude50\ude5c-\ude89\ude9d\udeb0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc2e\udc40\udc72-\udc8f\udd00-\udd06\udd08\udd09\udd0b-\udd30\udd46\udd60-\udd65\udd67\udd68\udd6a-\udd89\udd98\udee0-\udef2\udfb0]|\ud808[\udc00-\udf99]|\ud809[\udc80-\udd43]|\ud80b[\udf90-\udff0]|[\ud80c\ud81c-\ud820\ud822\ud840-\ud868\ud86a-\ud86c\ud86f-\ud872\ud874-\ud879\ud880-\ud883][\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\ude70-\udebe\uded0-\udeed\udf00-\udf2f\udf40-\udf43\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf50\udf93-\udf9f\udfe0\udfe1\udfe3]|\ud821[\udc00-\udff7]|\ud823[\udc00-\udcd5\udd00-\udd08]|\ud82b[\udff0-\udff3\udff5-\udffb\udffd\udffe]|\ud82c[\udc00-\udd22\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud837[\udf00-\udf1e]|\ud838[\udd00-\udd2c\udd37-\udd3d\udd4e\ude90-\udead\udec0-\udeeb]|\ud839[\udfe0-\udfe6\udfe8-\udfeb\udfed\udfee\udff0-\udffe]|\ud83a[\udc00-\udcc4\udd00-\udd43\udd4b]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud869[\udc00-\udedf\udf00-\udfff]|\ud86d[\udc00-\udf38\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]|\ud884[\udc00-\udf4a]"},{name:"LC",alias:"Cased_Letter",bmp:"A-Za-zµÀ-ÖØ-öø-ƺƼ-ƿǄ-ʓʕ-ʯͰ-ͳͶͷͻ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՠ-ֈႠ-ჅჇჍა-ჺჽ-ჿᎠ-Ᏽᏸ-ᏽᲀ-ᲈᲐ-ᲺᲽ-Ჿᴀ-ᴫᵫ-ᵷᵹ-ᶚḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℴℹℼ-ℿⅅ-ⅉⅎↃↄⰀ-ⱻⱾ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭꙀ-ꙭꚀ-ꚛꜢ-ꝯꝱ-ꞇꞋ-ꞎꞐ-ꟊꟐꟑꟓꟕ-ꟙꟵꟶꟺꬰ-ꭚꭠ-ꭨꭰ-ꮿﬀ-ﬆﬓ-ﬗＡ-Ｚａ-ｚ",astral:"\ud801[\udc00-\udc4f\udcb0-\udcd3\udcd8-\udcfb\udd70-\udd7a\udd7c-\udd8a\udd8c-\udd92\udd94\udd95\udd97-\udda1\udda3-\uddb1\uddb3-\uddb9\uddbb\uddbc]|\ud803[\udc80-\udcb2\udcc0-\udcf2]|\ud806[\udca0-\udcdf]|\ud81b[\ude40-\ude7f]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud837[\udf00-\udf09\udf0b-\udf1e]|\ud83a[\udd00-\udd43]"},{name:"Ll",alias:"Lowercase_Letter",bmp:"a-zµß-öø-ÿāăąćĉċčďđēĕėęěĝğġģĥħĩīĭįıĳĵķĸĺļľŀłńņňŉŋōŏőœŕŗřśŝşšţťŧũūŭůűųŵŷźżž-ƀƃƅƈƌƍƒƕƙ-ƛƞơƣƥƨƪƫƭưƴƶƹƺƽ-ƿǆǉǌǎǐǒǔǖǘǚǜǝǟǡǣǥǧǩǫǭǯǰǳǵǹǻǽǿȁȃȅȇȉȋȍȏȑȓȕȗșțȝȟȡȣȥȧȩȫȭȯȱȳ-ȹȼȿɀɂɇɉɋɍɏ-ʓʕ-ʯͱͳͷͻ-ͽΐά-ώϐϑϕ-ϗϙϛϝϟϡϣϥϧϩϫϭϯ-ϳϵϸϻϼа-џѡѣѥѧѩѫѭѯѱѳѵѷѹѻѽѿҁҋҍҏґғҕҗҙқҝҟҡңҥҧҩҫҭүұҳҵҷҹһҽҿӂӄӆӈӊӌӎӏӑӓӕӗәӛӝӟӡӣӥӧөӫӭӯӱӳӵӷӹӻӽӿԁԃԅԇԉԋԍԏԑԓԕԗԙԛԝԟԡԣԥԧԩԫԭԯՠ-ֈა-ჺჽ-ჿᏸ-ᏽᲀ-ᲈᴀ-ᴫᵫ-ᵷᵹ-ᶚḁḃḅḇḉḋḍḏḑḓḕḗḙḛḝḟḡḣḥḧḩḫḭḯḱḳḵḷḹḻḽḿṁṃṅṇṉṋṍṏṑṓṕṗṙṛṝṟṡṣṥṧṩṫṭṯṱṳṵṷṹṻṽṿẁẃẅẇẉẋẍẏẑẓẕ-ẝẟạảấầẩẫậắằẳẵặẹẻẽếềểễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹỻỽỿ-ἇἐ-ἕἠ-ἧἰ-ἷὀ-ὅὐ-ὗὠ-ὧὰ-ώᾀ-ᾇᾐ-ᾗᾠ-ᾧᾰ-ᾴᾶᾷιῂ-ῄῆῇῐ-ΐῖῗῠ-ῧῲ-ῴῶῷℊℎℏℓℯℴℹℼℽⅆ-ⅉⅎↄⰰ-ⱟⱡⱥⱦⱨⱪⱬⱱⱳⱴⱶ-ⱻⲁⲃⲅⲇⲉⲋⲍⲏⲑⲓⲕⲗⲙⲛⲝⲟⲡⲣⲥⲧⲩⲫⲭⲯⲱⲳⲵⲷⲹⲻⲽⲿⳁⳃⳅⳇⳉⳋⳍⳏⳑⳓⳕⳗⳙⳛⳝⳟⳡⳣⳤⳬⳮⳳⴀ-ⴥⴧⴭꙁꙃꙅꙇꙉꙋꙍꙏꙑꙓꙕꙗꙙꙛꙝꙟꙡꙣꙥꙧꙩꙫꙭꚁꚃꚅꚇꚉꚋꚍꚏꚑꚓꚕꚗꚙꚛꜣꜥꜧꜩꜫꜭꜯ-ꜱꜳꜵꜷꜹꜻꜽꜿꝁꝃꝅꝇꝉꝋꝍꝏꝑꝓꝕꝗꝙꝛꝝꝟꝡꝣꝥꝧꝩꝫꝭꝯꝱ-ꝸꝺꝼꝿꞁꞃꞅꞇꞌꞎꞑꞓ-ꞕꞗꞙꞛꞝꞟꞡꞣꞥꞧꞩꞯꞵꞷꞹꞻꞽꞿꟁꟃꟈꟊꟑꟓꟕꟗꟙꟶꟺꬰ-ꭚꭠ-ꭨꭰ-ꮿﬀ-ﬆﬓ-ﬗａ-ｚ",astral:"\ud801[\udc28-\udc4f\udcd8-\udcfb\udd97-\udda1\udda3-\uddb1\uddb3-\uddb9\uddbb\uddbc]|\ud803[\udcc0-\udcf2]|\ud806[\udcc0-\udcdf]|\ud81b[\ude60-\ude7f]|\ud835[\udc1a-\udc33\udc4e-\udc54\udc56-\udc67\udc82-\udc9b\udcb6-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udccf\udcea-\udd03\udd1e-\udd37\udd52-\udd6b\udd86-\udd9f\uddba-\uddd3\uddee-\ude07\ude22-\ude3b\ude56-\ude6f\ude8a-\udea5\udec2-\udeda\udedc-\udee1\udefc-\udf14\udf16-\udf1b\udf36-\udf4e\udf50-\udf55\udf70-\udf88\udf8a-\udf8f\udfaa-\udfc2\udfc4-\udfc9\udfcb]|\ud837[\udf00-\udf09\udf0b-\udf1e]|\ud83a[\udd22-\udd43]"},{name:"Lm",alias:"Modifier_Letter",bmp:"ʰ-ˁˆ-ˑˠ-ˤˬˮʹͺՙـۥۦߴߵߺࠚࠤࠨࣉॱๆໆჼៗᡃᪧᱸ-ᱽᴬ-ᵪᵸᶛ-ᶿⁱⁿₐ-ₜⱼⱽⵯⸯ々〱-〵〻ゝゞー-ヾꀕꓸ-ꓽꘌꙿꚜꚝꜗ-ꜟꝰꞈꟲ-ꟴꟸꟹꧏꧦꩰꫝꫳꫴꭜ-ꭟꭩｰﾞﾟ",astral:"\ud801[\udf80-\udf85\udf87-\udfb0\udfb2-\udfba]|\ud81a[\udf40-\udf43]|\ud81b[\udf93-\udf9f\udfe0\udfe1\udfe3]|\ud82b[\udff0-\udff3\udff5-\udffb\udffd\udffe]|\ud838[\udd37-\udd3d]|𞥋"},{name:"Lo",alias:"Other_Letter",bmp:"ªºƻǀ-ǃʔא-תׯ-ײؠ-ؿف-يٮٯٱ-ۓەۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪࠀ-ࠕࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣈऄ-हऽॐक़-ॡॲ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๅກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎᄀ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛱ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៜᠠ-ᡂᡄ-ᡸᢀ-ᢄᢇ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱷᳩ-ᳬᳮ-ᳳᳵᳶᳺℵ-ℸⴰ-ⵧⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ〆〼ぁ-ゖゟァ-ヺヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꀔꀖ-ꒌꓐ-ꓷꔀ-ꘋꘐ-ꘟꘪꘫꙮꚠ-ꛥꞏꟷꟻ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧠ-ꧤꧧ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩯꩱ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛꫜꫠ-ꫪꫲꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꯀ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎יִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼｦ-ｯｱ-ﾝﾠ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",astral:"\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf40\udf42-\udf49\udf50-\udf75\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf]|\ud801[\udc50-\udc9d\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe\uddbf\ude00\ude10-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udd00-\udd23\ude80-\udea9\udeb0\udeb1\udf00-\udf1c\udf27\udf30-\udf45\udf70-\udf81\udfb0-\udfc4\udfe0-\udff6]|\ud804[\udc03-\udc37\udc71\udc72\udc75\udc83-\udcaf\udcd0-\udce8\udd03-\udd26\udd44\udd47\udd50-\udd72\udd76\udd83-\uddb2\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude2b\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udede\udf05-\udf0c\udf0f\udf10\udf13-\udf28\udf2a-\udf30\udf32\udf33\udf35-\udf39\udf3d\udf50\udf5d-\udf61]|\ud805[\udc00-\udc34\udc47-\udc4a\udc5f-\udc61\udc80-\udcaf\udcc4\udcc5\udcc7\udd80-\uddae\uddd8-\udddb\ude00-\ude2f\ude44\ude80-\udeaa\udeb8\udf00-\udf1a\udf40-\udf46]|\ud806[\udc00-\udc2b\udcff-\udd06\udd09\udd0c-\udd13\udd15\udd16\udd18-\udd2f\udd3f\udd41\udda0-\udda7\uddaa-\uddd0\udde1\udde3\ude00\ude0b-\ude32\ude3a\ude50\ude5c-\ude89\ude9d\udeb0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc2e\udc40\udc72-\udc8f\udd00-\udd06\udd08\udd09\udd0b-\udd30\udd46\udd60-\udd65\udd67\udd68\udd6a-\udd89\udd98\udee0-\udef2\udfb0]|\ud808[\udc00-\udf99]|\ud809[\udc80-\udd43]|\ud80b[\udf90-\udff0]|[\ud80c\ud81c-\ud820\ud822\ud840-\ud868\ud86a-\ud86c\ud86f-\ud872\ud874-\ud879\ud880-\ud883][\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\ude70-\udebe\uded0-\udeed\udf00-\udf2f\udf63-\udf77\udf7d-\udf8f]|\ud81b[\udf00-\udf4a\udf50]|\ud821[\udc00-\udff7]|\ud823[\udc00-\udcd5\udd00-\udd08]|\ud82c[\udc00-\udd22\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99]|𝼊|\ud838[\udd00-\udd2c\udd4e\ude90-\udead\udec0-\udeeb]|\ud839[\udfe0-\udfe6\udfe8-\udfeb\udfed\udfee\udff0-\udffe]|\ud83a[\udc00-\udcc4]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud869[\udc00-\udedf\udf00-\udfff]|\ud86d[\udc00-\udf38\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]|\ud884[\udc00-\udf4a]"},{name:"Lt",alias:"Titlecase_Letter",bmp:"ǅǈǋǲᾈ-ᾏᾘ-ᾟᾨ-ᾯᾼῌῼ"},{name:"Lu",alias:"Uppercase_Letter",bmp:"A-ZÀ-ÖØ-ÞĀĂĄĆĈĊČĎĐĒĔĖĘĚĜĞĠĢĤĦĨĪĬĮİĲĴĶĹĻĽĿŁŃŅŇŊŌŎŐŒŔŖŘŚŜŞŠŢŤŦŨŪŬŮŰŲŴŶŸŹŻŽƁƂƄƆƇƉ-ƋƎ-ƑƓƔƖ-ƘƜƝƟƠƢƤƦƧƩƬƮƯƱ-ƳƵƷƸƼǄǇǊǍǏǑǓǕǗǙǛǞǠǢǤǦǨǪǬǮǱǴǶ-ǸǺǼǾȀȂȄȆȈȊȌȎȐȒȔȖȘȚȜȞȠȢȤȦȨȪȬȮȰȲȺȻȽȾɁɃ-ɆɈɊɌɎͰͲͶͿΆΈ-ΊΌΎΏΑ-ΡΣ-ΫϏϒ-ϔϘϚϜϞϠϢϤϦϨϪϬϮϴϷϹϺϽ-ЯѠѢѤѦѨѪѬѮѰѲѴѶѸѺѼѾҀҊҌҎҐҒҔҖҘҚҜҞҠҢҤҦҨҪҬҮҰҲҴҶҸҺҼҾӀӁӃӅӇӉӋӍӐӒӔӖӘӚӜӞӠӢӤӦӨӪӬӮӰӲӴӶӸӺӼӾԀԂԄԆԈԊԌԎԐԒԔԖԘԚԜԞԠԢԤԦԨԪԬԮԱ-ՖႠ-ჅჇჍᎠ-ᏵᲐ-ᲺᲽ-ᲿḀḂḄḆḈḊḌḎḐḒḔḖḘḚḜḞḠḢḤḦḨḪḬḮḰḲḴḶḸḺḼḾṀṂṄṆṈṊṌṎṐṒṔṖṘṚṜṞṠṢṤṦṨṪṬṮṰṲṴṶṸṺṼṾẀẂẄẆẈẊẌẎẐẒẔẞẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼẾỀỂỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴỶỸỺỼỾἈ-ἏἘ-ἝἨ-ἯἸ-ἿὈ-ὍὙὛὝὟὨ-ὯᾸ-ΆῈ-ΉῘ-ΊῨ-ῬῸ-Ώℂℇℋ-ℍℐ-ℒℕℙ-ℝℤΩℨK-ℭℰ-ℳℾℿⅅↃⰀ-ⰯⱠⱢ-ⱤⱧⱩⱫⱭ-ⱰⱲⱵⱾ-ⲀⲂⲄⲆⲈⲊⲌⲎⲐⲒⲔⲖⲘⲚⲜⲞⲠⲢⲤⲦⲨⲪⲬⲮⲰⲲⲴⲶⲸⲺⲼⲾⳀⳂⳄⳆⳈⳊⳌⳎⳐⳒⳔⳖⳘⳚⳜⳞⳠⳢⳫⳭⳲꙀꙂꙄꙆꙈꙊꙌꙎꙐꙒꙔꙖꙘꙚꙜꙞꙠꙢꙤꙦꙨꙪꙬꚀꚂꚄꚆꚈꚊꚌꚎꚐꚒꚔꚖꚘꚚꜢꜤꜦꜨꜪꜬꜮꜲꜴꜶꜸꜺꜼꜾꝀꝂꝄꝆꝈꝊꝌꝎꝐꝒꝔꝖꝘꝚꝜꝞꝠꝢꝤꝦꝨꝪꝬꝮꝹꝻꝽꝾꞀꞂꞄꞆꞋꞍꞐꞒꞖꞘꞚꞜꞞꞠꞢꞤꞦꞨꞪ-ꞮꞰ-ꞴꞶꞸꞺꞼꞾꟀꟂꟄ-ꟇꟉꟐꟖꟘꟵＡ-Ｚ",astral:"\ud801[\udc00-\udc27\udcb0-\udcd3\udd70-\udd7a\udd7c-\udd8a\udd8c-\udd92\udd94\udd95]|\ud803[\udc80-\udcb2]|\ud806[\udca0-\udcbf]|\ud81b[\ude40-\ude5f]|\ud835[\udc00-\udc19\udc34-\udc4d\udc68-\udc81\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb5\udcd0-\udce9\udd04\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd38\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd6c-\udd85\udda0-\uddb9\uddd4-\udded\ude08-\ude21\ude3c-\ude55\ude70-\ude89\udea8-\udec0\udee2-\udefa\udf1c-\udf34\udf56-\udf6e\udf90-\udfa8\udfca]|\ud83a[\udd00-\udd21]"},{name:"M",alias:"Mark",bmp:"̀-ͯ҃-҉֑-ׇֽֿׁׂׅׄؐ-ًؚ-ٰٟۖ-ۜ۟-۪ۤۧۨ-ܑۭܰ-݊ަ-ް߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࢘-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣঁ-ঃ়া-ৄেৈো-্ৗৢৣ৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑੰੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣஂா-ூெ-ைொ-்ௗఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣඁ-ඃ්ා-ුූෘ-ෟෲෳัิ-ฺ็-๎ັິ-ຼ່-ໍ༹༘༙༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏႚ-ႝ፝-፟ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝᠋-᠍᠏ᢅᢆᢩᤠ-ᤫᤰ-᤻ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼᪰-ᫎᬀ-ᬄ᬴-᭄᭫-᭳ᮀ-ᮂᮡ-ᮭ᯦-᯳ᰤ-᰷᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿⃐-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꙯-꙲ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣠-꣱ꣿꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀ꧥꨩ-ꨶꩃꩌꩍꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭ﬞ︀-️︠-︯",astral:"\ud800[\uddfd\udee0\udf76-\udf7a]|\ud802[\ude01-\ude03\ude05\ude06\ude0c-\ude0f\ude38-\ude3a\ude3f\udee5\udee6]|\ud803[\udd24-\udd27\udeab\udeac\udf46-\udf50\udf82-\udf85]|\ud804[\udc00-\udc02\udc38-\udc46\udc70\udc73\udc74\udc7f-\udc82\udcb0-\udcba\udcc2\udd00-\udd02\udd27-\udd34\udd45\udd46\udd73\udd80-\udd82\uddb3-\uddc0\uddc9-\uddcc\uddce\uddcf\ude2c-\ude37\ude3e\udedf-\udeea\udf00-\udf03\udf3b\udf3c\udf3e-\udf44\udf47\udf48\udf4b-\udf4d\udf57\udf62\udf63\udf66-\udf6c\udf70-\udf74]|\ud805[\udc35-\udc46\udc5e\udcb0-\udcc3\uddaf-\uddb5\uddb8-\uddc0\udddc\udddd\ude30-\ude40\udeab-\udeb7\udf1d-\udf2b]|\ud806[\udc2c-\udc3a\udd30-\udd35\udd37\udd38\udd3b-\udd3e\udd40\udd42\udd43\uddd1-\uddd7\uddda-\udde0\udde4\ude01-\ude0a\ude33-\ude39\ude3b-\ude3e\ude47\ude51-\ude5b\ude8a-\ude99]|\ud807[\udc2f-\udc36\udc38-\udc3f\udc92-\udca7\udca9-\udcb6\udd31-\udd36\udd3a\udd3c\udd3d\udd3f-\udd45\udd47\udd8a-\udd8e\udd90\udd91\udd93-\udd97\udef3-\udef6]|\ud81a[\udef0-\udef4\udf30-\udf36]|\ud81b[\udf4f\udf51-\udf87\udf8f-\udf92\udfe4\udff0\udff1]|\ud82f[\udc9d\udc9e]|\ud833[\udf00-\udf2d\udf30-\udf46]|\ud834[\udd65-\udd69\udd6d-\udd72\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad\ude42-\ude44]|\ud836[\ude00-\ude36\ude3b-\ude6c\ude75\ude84\ude9b-\ude9f\udea1-\udeaf]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23\udc24\udc26-\udc2a\udd30-\udd36\udeae\udeec-\udeef]|\ud83a[\udcd0-\udcd6\udd44-\udd4a]|\udb40[\udd00-\uddef]"},{name:"Mc",alias:"Spacing_Mark",bmp:"ःऻा-ीॉ-ौॎॏংঃা-ীেৈোৌৗਃਾ-ੀઃા-ીૉોૌଂଃାୀେୈୋୌୗாிுூெ-ைொ-ௌௗఁ-ఃు-ౄಂಃಾೀ-ೄೇೈೊೋೕೖംഃാ-ീെ-ൈൊ-ൌൗංඃා-ෑෘ-ෟෲෳ༾༿ཿါာေးျြၖၗၢ-ၤၧ-ၭႃႄႇ-ႌႏႚ-ႜ᜕᜴ាើ-ៅះៈᤣ-ᤦᤩ-ᤫᤰᤱᤳ-ᤸᨙᨚᩕᩗᩡᩣᩤᩭ-ᩲᬄᬵᬻᬽ-ᭁᭃ᭄ᮂᮡᮦᮧ᮪ᯧᯪ-ᯬᯮ᯲᯳ᰤ-ᰫᰴᰵ᳡᳷〮〯ꠣꠤꠧꢀꢁꢴ-ꣃꥒ꥓ꦃꦴꦵꦺꦻꦾ-꧀ꨯꨰꨳꨴꩍꩻꩽꫫꫮꫯꫵꯣꯤꯦꯧꯩꯪ꯬",astral:"\ud804[\udc00\udc02\udc82\udcb0-\udcb2\udcb7\udcb8\udd2c\udd45\udd46\udd82\uddb3-\uddb5\uddbf\uddc0\uddce\ude2c-\ude2e\ude32\ude33\ude35\udee0-\udee2\udf02\udf03\udf3e\udf3f\udf41-\udf44\udf47\udf48\udf4b-\udf4d\udf57\udf62\udf63]|\ud805[\udc35-\udc37\udc40\udc41\udc45\udcb0-\udcb2\udcb9\udcbb-\udcbe\udcc1\uddaf-\uddb1\uddb8-\uddbb\uddbe\ude30-\ude32\ude3b\ude3c\ude3e\udeac\udeae\udeaf\udeb6\udf20\udf21\udf26]|\ud806[\udc2c-\udc2e\udc38\udd30-\udd35\udd37\udd38\udd3d\udd40\udd42\uddd1-\uddd3\udddc-\udddf\udde4\ude39\ude57\ude58\ude97]|\ud807[\udc2f\udc3e\udca9\udcb1\udcb4\udd8a-\udd8e\udd93\udd94\udd96\udef5\udef6]|\ud81b[\udf51-\udf87\udff0\udff1]|\ud834[\udd65\udd66\udd6d-\udd72]"},{name:"Me",alias:"Enclosing_Mark",bmp:"҈҉᪾⃝-⃠⃢-⃤꙰-꙲"},{name:"Mn",alias:"Nonspacing_Mark",bmp:"̀-ͯ҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-ٰٟۖ-ۜ۟-۪ۤۧۨ-ܑۭܰ-݊ަ-ް߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࢘-࢟࣊-ࣣ࣡-ंऺ़ु-ै्॑-ॗॢॣঁ়ু-ৄ্ৢৣ৾ਁਂ਼ੁੂੇੈੋ-੍ੑੰੱੵઁં઼ુ-ૅેૈ્ૢૣૺ-૿ଁ଼ିୁ-ୄ୍୕ୖୢୣஂீ்ఀఄ఼ా-ీె-ైొ-్ౕౖౢౣಁ಼ಿೆೌ್ೢೣഀഁ഻഼ു-ൄ്ൢൣඁ්ි-ුූัิ-ฺ็-๎ັິ-ຼ່-ໍཱ༹༘༙༵༷-ཾྀ-྄྆྇ྍ-ྗྙ-ྼ࿆ိ-ူဲ-့္်ွှၘၙၞ-ၠၱ-ၴႂႅႆႍႝ፝-፟ᜒ-᜔ᜲᜳᝒᝓᝲᝳ឴឵ិ-ួំ៉-៓៝᠋-᠍᠏ᢅᢆᢩᤠ-ᤢᤧᤨᤲ᤹-᤻ᨘᨗᨛᩖᩘ-ᩞ᩠ᩢᩥ-ᩬᩳ-᩿᩼᪰-᪽ᪿ-ᫎᬀ-ᬃ᬴ᬶ-ᬺᬼᭂ᭫-᭳ᮀᮁᮢ-ᮥᮨᮩ᮫-ᮭ᯦ᯨᯩᯭᯯ-ᯱᰬ-ᰳᰶ᰷᳐-᳔᳒-᳢᳠-᳨᳭᳴᳸᳹᷀-᷿⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〭꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠥꠦ꠬꣄ꣅ꣠-꣱ꣿꤦ-꤭ꥇ-ꥑꦀ-ꦂ꦳ꦶ-ꦹꦼꦽꧥꨩ-ꨮꨱꨲꨵꨶꩃꩌꩼꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫬꫭ꫶ꯥꯨ꯭ﬞ︀-️︠-︯",astral:"\ud800[\uddfd\udee0\udf76-\udf7a]|\ud802[\ude01-\ude03\ude05\ude06\ude0c-\ude0f\ude38-\ude3a\ude3f\udee5\udee6]|\ud803[\udd24-\udd27\udeab\udeac\udf46-\udf50\udf82-\udf85]|\ud804[\udc01\udc38-\udc46\udc70\udc73\udc74\udc7f-\udc81\udcb3-\udcb6\udcb9\udcba\udcc2\udd00-\udd02\udd27-\udd2b\udd2d-\udd34\udd73\udd80\udd81\uddb6-\uddbe\uddc9-\uddcc\uddcf\ude2f-\ude31\ude34\ude36\ude37\ude3e\udedf\udee3-\udeea\udf00\udf01\udf3b\udf3c\udf40\udf66-\udf6c\udf70-\udf74]|\ud805[\udc38-\udc3f\udc42-\udc44\udc46\udc5e\udcb3-\udcb8\udcba\udcbf\udcc0\udcc2\udcc3\uddb2-\uddb5\uddbc\uddbd\uddbf\uddc0\udddc\udddd\ude33-\ude3a\ude3d\ude3f\ude40\udeab\udead\udeb0-\udeb5\udeb7\udf1d-\udf1f\udf22-\udf25\udf27-\udf2b]|\ud806[\udc2f-\udc37\udc39\udc3a\udd3b\udd3c\udd3e\udd43\uddd4-\uddd7\uddda\udddb\udde0\ude01-\ude0a\ude33-\ude38\ude3b-\ude3e\ude47\ude51-\ude56\ude59-\ude5b\ude8a-\ude96\ude98\ude99]|\ud807[\udc30-\udc36\udc38-\udc3d\udc3f\udc92-\udca7\udcaa-\udcb0\udcb2\udcb3\udcb5\udcb6\udd31-\udd36\udd3a\udd3c\udd3d\udd3f-\udd45\udd47\udd90\udd91\udd95\udd97\udef3\udef4]|\ud81a[\udef0-\udef4\udf30-\udf36]|\ud81b[\udf4f\udf8f-\udf92\udfe4]|\ud82f[\udc9d\udc9e]|\ud833[\udf00-\udf2d\udf30-\udf46]|\ud834[\udd67-\udd69\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad\ude42-\ude44]|\ud836[\ude00-\ude36\ude3b-\ude6c\ude75\ude84\ude9b-\ude9f\udea1-\udeaf]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23\udc24\udc26-\udc2a\udd30-\udd36\udeae\udeec-\udeef]|\ud83a[\udcd0-\udcd6\udd44-\udd4a]|\udb40[\udd00-\uddef]"},{name:"N",alias:"Number",bmp:"0-9²³¹¼-¾٠-٩۰-۹߀-߉०-९০-৯৴-৹੦-੯૦-૯୦-୯୲-୷௦-௲౦-౯౸-౾೦-೯൘-൞൦-൸෦-෯๐-๙໐-໙༠-༳၀-၉႐-႙፩-፼ᛮ-ᛰ០-៩៰-៹᠐-᠙᥆-᥏᧐-᧚᪀-᪉᪐-᪙᭐-᭙᮰-᮹᱀-᱉᱐-᱙⁰⁴-⁹₀-₉⅐-ↂↅ-↉①-⒛⓪-⓿❶-➓⳽〇〡-〩〸-〺㆒-㆕㈠-㈩㉈-㉏㉑-㉟㊀-㊉㊱-㊿꘠-꘩ꛦ-ꛯ꠰-꠵꣐-꣙꤀-꤉꧐-꧙꧰-꧹꩐-꩙꯰-꯹０-９",astral:"\ud800[\udd07-\udd33\udd40-\udd78\udd8a\udd8b\udee1-\udefb\udf20-\udf23\udf41\udf4a\udfd1-\udfd5]|\ud801[\udca0-\udca9]|\ud802[\udc58-\udc5f\udc79-\udc7f\udca7-\udcaf\udcfb-\udcff\udd16-\udd1b\uddbc\uddbd\uddc0-\uddcf\uddd2-\uddff\ude40-\ude48\ude7d\ude7e\ude9d-\ude9f\udeeb-\udeef\udf58-\udf5f\udf78-\udf7f\udfa9-\udfaf]|\ud803[\udcfa-\udcff\udd30-\udd39\ude60-\ude7e\udf1d-\udf26\udf51-\udf54\udfc5-\udfcb]|\ud804[\udc52-\udc6f\udcf0-\udcf9\udd36-\udd3f\uddd0-\uddd9\udde1-\uddf4\udef0-\udef9]|\ud805[\udc50-\udc59\udcd0-\udcd9\ude50-\ude59\udec0-\udec9\udf30-\udf3b]|\ud806[\udce0-\udcf2\udd50-\udd59]|\ud807[\udc50-\udc6c\udd50-\udd59\udda0-\udda9\udfc0-\udfd4]|\ud809[\udc00-\udc6e]|\ud81a[\ude60-\ude69\udec0-\udec9\udf50-\udf59\udf5b-\udf61]|\ud81b[\ude80-\ude96]|\ud834[\udee0-\udef3\udf60-\udf78]|\ud835[\udfce-\udfff]|\ud838[\udd40-\udd49\udef0-\udef9]|\ud83a[\udcc7-\udccf\udd50-\udd59]|\ud83b[\udc71-\udcab\udcad-\udcaf\udcb1-\udcb4\udd01-\udd2d\udd2f-\udd3d]|\ud83c[\udd00-\udd0c]|\ud83e[\udff0-\udff9]"},{name:"Nd",alias:"Decimal_Number",bmp:"0-9٠-٩۰-۹߀-߉०-९০-৯੦-੯૦-૯୦-୯௦-௯౦-౯೦-೯൦-൯෦-෯๐-๙໐-໙༠-༩၀-၉႐-႙០-៩᠐-᠙᥆-᥏᧐-᧙᪀-᪉᪐-᪙᭐-᭙᮰-᮹᱀-᱉᱐-᱙꘠-꘩꣐-꣙꤀-꤉꧐-꧙꧰-꧹꩐-꩙꯰-꯹０-９",astral:"\ud801[\udca0-\udca9]|\ud803[\udd30-\udd39]|\ud804[\udc66-\udc6f\udcf0-\udcf9\udd36-\udd3f\uddd0-\uddd9\udef0-\udef9]|\ud805[\udc50-\udc59\udcd0-\udcd9\ude50-\ude59\udec0-\udec9\udf30-\udf39]|\ud806[\udce0-\udce9\udd50-\udd59]|\ud807[\udc50-\udc59\udd50-\udd59\udda0-\udda9]|\ud81a[\ude60-\ude69\udec0-\udec9\udf50-\udf59]|\ud835[\udfce-\udfff]|\ud838[\udd40-\udd49\udef0-\udef9]|\ud83a[\udd50-\udd59]|\ud83e[\udff0-\udff9]"},{name:"Nl",alias:"Letter_Number",bmp:"ᛮ-ᛰⅠ-ↂↅ-ↈ〇〡-〩〸-〺ꛦ-ꛯ",astral:"\ud800[\udd40-\udd74\udf41\udf4a\udfd1-\udfd5]|\ud809[\udc00-\udc6e]"},{name:"No",alias:"Other_Number",bmp:"²³¹¼-¾৴-৹୲-୷௰-௲౸-౾൘-൞൰-൸༪-༳፩-፼៰-៹᧚⁰⁴-⁹₀-₉⅐-⅟↉①-⒛⓪-⓿❶-➓⳽㆒-㆕㈠-㈩㉈-㉏㉑-㉟㊀-㊉㊱-㊿꠰-꠵",astral:"\ud800[\udd07-\udd33\udd75-\udd78\udd8a\udd8b\udee1-\udefb\udf20-\udf23]|\ud802[\udc58-\udc5f\udc79-\udc7f\udca7-\udcaf\udcfb-\udcff\udd16-\udd1b\uddbc\uddbd\uddc0-\uddcf\uddd2-\uddff\ude40-\ude48\ude7d\ude7e\ude9d-\ude9f\udeeb-\udeef\udf58-\udf5f\udf78-\udf7f\udfa9-\udfaf]|\ud803[\udcfa-\udcff\ude60-\ude7e\udf1d-\udf26\udf51-\udf54\udfc5-\udfcb]|\ud804[\udc52-\udc65\udde1-\uddf4]|\ud805[\udf3a\udf3b]|\ud806[\udcea-\udcf2]|\ud807[\udc5a-\udc6c\udfc0-\udfd4]|\ud81a[\udf5b-\udf61]|\ud81b[\ude80-\ude96]|\ud834[\udee0-\udef3\udf60-\udf78]|\ud83a[\udcc7-\udccf]|\ud83b[\udc71-\udcab\udcad-\udcaf\udcb1-\udcb4\udd01-\udd2d\udd2f-\udd3d]|\ud83c[\udd00-\udd0c]"},{name:"P",alias:"Punctuation",bmp:"!-#%-\\*,-\\/:;\\?@\\[-\\]_\\{\\}¡§«¶·»¿;·՚-՟։֊־׀׃׆׳״؉؊،؍؛؝-؟٪-٭۔܀-܍߷-߹࠰-࠾࡞।॥॰৽੶૰౷಄෴๏๚๛༄-༒༔༺-༽྅࿐-࿔࿙࿚၊-၏჻፠-፨᐀᙮᚛᚜᛫-᛭᜵᜶។-៖៘-៚᠀-᠊᥄᥅᨞᨟᪠-᪦᪨-᪭᭚-᭠᭽᭾᯼-᯿᰻-᰿᱾᱿᳀-᳇᳓‐-‧‰-⁃⁅-⁑⁓-⁞⁽⁾₍₎⌈-⌋〈〉❨-❵⟅⟆⟦-⟯⦃-⦘⧘-⧛⧼⧽⳹-⳼⳾⳿⵰⸀-⸮⸰-⹏⹒-⹝、-〃〈-】〔-〟〰〽゠・꓾꓿꘍-꘏꙳꙾꛲-꛷꡴-꡷꣎꣏꣸-꣺꣼꤮꤯꥟꧁-꧍꧞꧟꩜-꩟꫞꫟꫰꫱꯫﴾﴿︐-︙︰-﹒﹔-﹡﹣﹨﹪﹫！-＃％-＊，-／：；？＠［-］＿｛｝｟-･",astral:"\ud800[\udd00-\udd02\udf9f\udfd0]|𐕯|\ud802[\udc57\udd1f\udd3f\ude50-\ude58\ude7f\udef0-\udef6\udf39-\udf3f\udf99-\udf9c]|\ud803[\udead\udf55-\udf59\udf86-\udf89]|\ud804[\udc47-\udc4d\udcbb\udcbc\udcbe-\udcc1\udd40-\udd43\udd74\udd75\uddc5-\uddc8\uddcd\udddb\udddd-\udddf\ude38-\ude3d\udea9]|\ud805[\udc4b-\udc4f\udc5a\udc5b\udc5d\udcc6\uddc1-\uddd7\ude41-\ude43\ude60-\ude6c\udeb9\udf3c-\udf3e]|\ud806[\udc3b\udd44-\udd46\udde2\ude3f-\ude46\ude9a-\ude9c\ude9e-\udea2]|\ud807[\udc41-\udc45\udc70\udc71\udef7\udef8\udfff]|\ud809[\udc70-\udc74]|\ud80b[\udff1\udff2]|\ud81a[\ude6e\ude6f\udef5\udf37-\udf3b\udf44]|\ud81b[\ude97-\ude9a\udfe2]|𛲟|\ud836[\ude87-\ude8b]|\ud83a[\udd5e\udd5f]"},{name:"Pc",alias:"Connector_Punctuation",bmp:"_‿⁀⁔︳︴﹍-﹏＿"},{name:"Pd",alias:"Dash_Punctuation",bmp:"\\-֊־᐀᠆‐-―⸗⸚⸺⸻⹀⹝〜〰゠︱︲﹘﹣－",astral:"𐺭"},{name:"Pe",alias:"Close_Punctuation",bmp:"\\)\\]\\}༻༽᚜⁆⁾₎⌉⌋〉❩❫❭❯❱❳❵⟆⟧⟩⟫⟭⟯⦄⦆⦈⦊⦌⦎⦐⦒⦔⦖⦘⧙⧛⧽⸣⸥⸧⸩⹖⹘⹚⹜〉》」』】〕〗〙〛〞〟﴾︘︶︸︺︼︾﹀﹂﹄﹈﹚﹜﹞）］｝｠｣"},{name:"Pf",alias:"Final_Punctuation",bmp:"»’”›⸃⸅⸊⸍⸝⸡"},{name:"Pi",alias:"Initial_Punctuation",bmp:"«‘‛“‟‹⸂⸄⸉⸌⸜⸠"},{name:"Po",alias:"Other_Punctuation",bmp:"!-#%-'\\*,\\.\\/:;\\?@\\¡§¶·¿;·՚-՟։׀׃׆׳״؉؊،؍؛؝-؟٪-٭۔܀-܍߷-߹࠰-࠾࡞।॥॰৽੶૰౷಄෴๏๚๛༄-༒༔྅࿐-࿔࿙࿚၊-၏჻፠-፨᙮᛫-᛭᜵᜶។-៖៘-៚᠀-᠅᠇-᠊᥄᥅᨞᨟᪠-᪦᪨-᪭᭚-᭠᭽᭾᯼-᯿᰻-᰿᱾᱿᳀-᳇᳓‖‗†-‧‰-‸※-‾⁁-⁃⁇-⁑⁓⁕-⁞⳹-⳼⳾⳿⵰⸀⸁⸆-⸈⸋⸎-⸖⸘⸙⸛⸞⸟⸪-⸮⸰-⸹⸼-⸿⹁⹃-⹏⹒-⹔、-〃〽・꓾꓿꘍-꘏꙳꙾꛲-꛷꡴-꡷꣎꣏꣸-꣺꣼꤮꤯꥟꧁-꧍꧞꧟꩜-꩟꫞꫟꫰꫱꯫︐-︖︙︰﹅﹆﹉-﹌﹐-﹒﹔-﹗﹟-﹡﹨﹪﹫！-＃％-＇＊，．／：；？＠＼｡､･",astral:"\ud800[\udd00-\udd02\udf9f\udfd0]|𐕯|\ud802[\udc57\udd1f\udd3f\ude50-\ude58\ude7f\udef0-\udef6\udf39-\udf3f\udf99-\udf9c]|\ud803[\udf55-\udf59\udf86-\udf89]|\ud804[\udc47-\udc4d\udcbb\udcbc\udcbe-\udcc1\udd40-\udd43\udd74\udd75\uddc5-\uddc8\uddcd\udddb\udddd-\udddf\ude38-\ude3d\udea9]|\ud805[\udc4b-\udc4f\udc5a\udc5b\udc5d\udcc6\uddc1-\uddd7\ude41-\ude43\ude60-\ude6c\udeb9\udf3c-\udf3e]|\ud806[\udc3b\udd44-\udd46\udde2\ude3f-\ude46\ude9a-\ude9c\ude9e-\udea2]|\ud807[\udc41-\udc45\udc70\udc71\udef7\udef8\udfff]|\ud809[\udc70-\udc74]|\ud80b[\udff1\udff2]|\ud81a[\ude6e\ude6f\udef5\udf37-\udf3b\udf44]|\ud81b[\ude97-\ude9a\udfe2]|𛲟|\ud836[\ude87-\ude8b]|\ud83a[\udd5e\udd5f]"},{name:"Ps",alias:"Open_Punctuation",bmp:"\\(\\[\\{༺༼᚛‚„⁅⁽₍⌈⌊〈❨❪❬❮❰❲❴⟅⟦⟨⟪⟬⟮⦃⦅⦇⦉⦋⦍⦏⦑⦓⦕⦗⧘⧚⧼⸢⸤⸦⸨⹂⹕⹗⹙⹛〈《「『【〔〖〘〚〝﴿︗︵︷︹︻︽︿﹁﹃﹇﹙﹛﹝（［｛｟｢"},{name:"S",alias:"Symbol",bmp:"\\$\\+<->\\^`\\|~¢-¦¨©¬®-±´¸×÷˂-˅˒-˟˥-˫˭˯-˿͵΄΅϶҂֍-֏؆-؈؋؎؏۞۩۽۾߶߾߿࢈৲৳৺৻૱୰௳-௺౿൏൹฿༁-༃༓༕-༗༚-༟༴༶༸྾-࿅࿇-࿌࿎࿏࿕-࿘႞႟᎐-᎙᙭៛᥀᧞-᧿᭡-᭪᭴-᭼᾽᾿-῁῍-῏῝-῟῭-`´῾⁄⁒⁺-⁼₊-₌₠-⃀℀℁℃-℆℈℉℔№-℘℞-℣℥℧℩℮℺℻⅀-⅄⅊-⅍⅏↊↋←-⌇⌌-⌨⌫-␦⑀-⑊⒜-ⓩ─-❧➔-⟄⟇-⟥⟰-⦂⦙-⧗⧜-⧻⧾-⭳⭶-⮕⮗-⯿⳥-⳪⹐⹑⺀-⺙⺛-⻳⼀-⿕⿰-⿻〄〒〓〠〶〷〾〿゛゜㆐㆑㆖-㆟㇀-㇣㈀-㈞㈪-㉇㉐㉠-㉿㊊-㊰㋀-㏿䷀-䷿꒐-꓆꜀-꜖꜠꜡꞉꞊꠨-꠫꠶-꠹꩷-꩹꭛꭪꭫﬩﮲-﯂﵀-﵏﷏﷼-﷿﹢﹤-﹦﹩＄＋＜-＞＾｀｜～￠-￦￨-￮￼�",astral:"\ud800[\udd37-\udd3f\udd79-\udd89\udd8c-\udd8e\udd90-\udd9c\udda0\uddd0-\uddfc]|\ud802[\udc77\udc78\udec8]|𑜿|\ud807[\udfd5-\udff1]|\ud81a[\udf3c-\udf3f\udf45]|𛲜|\ud833[\udf50-\udfc3]|\ud834[\udc00-\udcf5\udd00-\udd26\udd29-\udd64\udd6a-\udd6c\udd83\udd84\udd8c-\udda9\uddae-\uddea\ude00-\ude41\ude45\udf00-\udf56]|\ud835[\udec1\udedb\udefb\udf15\udf35\udf4f\udf6f\udf89\udfa9\udfc3]|\ud836[\udc00-\uddff\ude37-\ude3a\ude6d-\ude74\ude76-\ude83\ude85\ude86]|\ud838[\udd4f\udeff]|\ud83b[\udcac\udcb0\udd2e\udef0\udef1]|\ud83c[\udc00-\udc2b\udc30-\udc93\udca0-\udcae\udcb1-\udcbf\udcc1-\udccf\udcd1-\udcf5\udd0d-\uddad\udde6-\ude02\ude10-\ude3b\ude40-\ude48\ude50\ude51\ude60-\ude65\udf00-\udfff]|\ud83d[\udc00-\uded7\udedd-\udeec\udef0-\udefc\udf00-\udf73\udf80-\udfd8\udfe0-\udfeb\udff0]|\ud83e[\udc00-\udc0b\udc10-\udc47\udc50-\udc59\udc60-\udc87\udc90-\udcad\udcb0\udcb1\udd00-\ude53\ude60-\ude6d\ude70-\ude74\ude78-\ude7c\ude80-\ude86\ude90-\udeac\udeb0-\udeba\udec0-\udec5\uded0-\uded9\udee0-\udee7\udef0-\udef6\udf00-\udf92\udf94-\udfca]"},{name:"Sc",alias:"Currency_Symbol",bmp:"\\$¢-¥֏؋߾߿৲৳৻૱௹฿៛₠-⃀꠸﷼﹩＄￠￡￥￦",astral:"\ud807[\udfdd-\udfe0]|𞋿|𞲰"},{name:"Sk",alias:"Modifier_Symbol",bmp:"\\^`¨¯´¸˂-˅˒-˟˥-˫˭˯-˿͵΄΅࢈᾽᾿-῁῍-῏῝-῟῭-`´῾゛゜꜀-꜖꜠꜡꞉꞊꭛꭪꭫﮲-﯂＾｀￣",astral:"\ud83c[\udffb-\udfff]"},{name:"Sm",alias:"Math_Symbol",bmp:"\\+<->\\|~¬±×÷϶؆-؈⁄⁒⁺-⁼₊-₌℘⅀-⅄⅋←-↔↚↛↠↣↦↮⇎⇏⇒⇔⇴-⋿⌠⌡⍼⎛-⎳⏜-⏡▷◁◸-◿♯⟀-⟄⟇-⟥⟰-⟿⤀-⦂⦙-⧗⧜-⧻⧾-⫿⬰-⭄⭇-⭌﬩﹢﹤-﹦＋＜-＞｜～￢￩-￬",astral:"\ud835[\udec1\udedb\udefb\udf15\udf35\udf4f\udf6f\udf89\udfa9\udfc3]|\ud83b[\udef0\udef1]"},{name:"So",alias:"Other_Symbol",bmp:"¦©®°҂֍֎؎؏۞۩۽۾߶৺୰௳-௸௺౿൏൹༁-༃༓༕-༗༚-༟༴༶༸྾-࿅࿇-࿌࿎࿏࿕-࿘႞႟᎐-᎙᙭᥀᧞-᧿᭡-᭪᭴-᭼℀℁℃-℆℈℉℔№℗℞-℣℥℧℩℮℺℻⅊⅌⅍⅏↊↋↕-↙↜-↟↡↢↤↥↧-↭↯-⇍⇐⇑⇓⇕-⇳⌀-⌇⌌-⌟⌢-⌨⌫-⍻⍽-⎚⎴-⏛⏢-␦⑀-⑊⒜-ⓩ─-▶▸-◀◂-◷☀-♮♰-❧➔-➿⠀-⣿⬀-⬯⭅⭆⭍-⭳⭶-⮕⮗-⯿⳥-⳪⹐⹑⺀-⺙⺛-⻳⼀-⿕⿰-⿻〄〒〓〠〶〷〾〿㆐㆑㆖-㆟㇀-㇣㈀-㈞㈪-㉇㉐㉠-㉿㊊-㊰㋀-㏿䷀-䷿꒐-꓆꠨-꠫꠶꠷꠹꩷-꩹﵀-﵏﷏﷽-﷿￤￨￭￮￼�",astral:"\ud800[\udd37-\udd3f\udd79-\udd89\udd8c-\udd8e\udd90-\udd9c\udda0\uddd0-\uddfc]|\ud802[\udc77\udc78\udec8]|𑜿|\ud807[\udfd5-\udfdc\udfe1-\udff1]|\ud81a[\udf3c-\udf3f\udf45]|𛲜|\ud833[\udf50-\udfc3]|\ud834[\udc00-\udcf5\udd00-\udd26\udd29-\udd64\udd6a-\udd6c\udd83\udd84\udd8c-\udda9\uddae-\uddea\ude00-\ude41\ude45\udf00-\udf56]|\ud836[\udc00-\uddff\ude37-\ude3a\ude6d-\ude74\ude76-\ude83\ude85\ude86]|𞅏|\ud83b[\udcac\udd2e]|\ud83c[\udc00-\udc2b\udc30-\udc93\udca0-\udcae\udcb1-\udcbf\udcc1-\udccf\udcd1-\udcf5\udd0d-\uddad\udde6-\ude02\ude10-\ude3b\ude40-\ude48\ude50\ude51\ude60-\ude65\udf00-\udffa]|\ud83d[\udc00-\uded7\udedd-\udeec\udef0-\udefc\udf00-\udf73\udf80-\udfd8\udfe0-\udfeb\udff0]|\ud83e[\udc00-\udc0b\udc10-\udc47\udc50-\udc59\udc60-\udc87\udc90-\udcad\udcb0\udcb1\udd00-\ude53\ude60-\ude6d\ude70-\ude74\ude78-\ude7c\ude80-\ude86\ude90-\udeac\udeb0-\udeba\udec0-\udec5\uded0-\uded9\udee0-\udee7\udef0-\udef6\udf00-\udf92\udf94-\udfca]"},{name:"Z",alias:"Separator",bmp:"    - \u2028\u2029  　"},{name:"Zl",alias:"Line_Separator",bmp:"\u2028"},{name:"Zp",alias:"Paragraph_Separator",bmp:"\u2029"},{name:"Zs",alias:"Space_Separator",bmp:"    -   　"}]},{}]},{},[3])(3)}));

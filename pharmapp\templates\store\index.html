{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NAZZ | LOGIN</title>
    <style>
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: rgb(183, 239, 239);
            font-family: Arial, Helvetica, sans-serif;
            background-image: url("{% static 'images/Pharmacists.jpg' %}");
            background-size: cover;
            background-position: center;
            margin: 0;
        }

        .form-box {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 30%;
            padding: 2em;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 15px;
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .form-box:hover {
            transform: scale(1.02);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
        }

        .form-box h4 {
            text-align: center;
            color: #fff;
            font-size: 1.8rem;
            margin-bottom: 1em;
            letter-spacing: 1.2px;
            text-transform: uppercase;
        }

        .form_input {
            margin-bottom: 1.5em;
            width: 100%;
        }

        .form_input input {
            width: 100%;
            padding: 10px;
            font-size: 1rem;
            border: none;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            outline: none;
            transition: box-shadow 0.3s ease;
        }

        .form_input input:focus {
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        button {
            width: 110%;
            padding: 10px;
            font-size: 1rem;
            background-color: #28a745;
            border: none;
            color: #fff;
            border-radius: 5px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        button:hover {
            background-color: #218838;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .alert {
            margin-bottom: 2em;
            color: #ff4d4d;
            font-weight: bold;
            font-size: 1rem;
            text-align: center;
        }

        /* Media Query for tablets and smaller screens */
        @media (max-width: 768px) {
            .form-box {
                width: 80%;
                padding: 1.5em;
            }
        }

        /* Media Query for mobile screens */
        @media (max-width: 480px) {
            .form-box {
                width: 90%;
                padding: 1em;
            }

            .form-box h4 {
                font-size: 1.5rem;
            }

            .form_input input {
                font-size: 0.9rem;
            }

            button {
                font-size: 0.9rem;
            }
        }
    </style>

</head>

<body>
    {% for message in messages %}
    <div style="text-align: center;" class="alert alert-{{ message.tags }}">{{ message }}</div>
    {% endfor %}
    <div class="container">
        <div class="form-box">
            <form action="" method="POST">
                <h4>LOGIN FORM</h4>
                {% csrf_token %}
                <div class="form_input"><input type="text" name="mobile" placeholder="Mobile Number"></div>
                <div class="form_input"><input type="password" name="password" placeholder="Password"></div>
                <button>LOGIN</button>
            </form>
        </div>
    </div>
</body>

</html>

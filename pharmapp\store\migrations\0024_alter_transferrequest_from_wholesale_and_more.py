# Generated by Django 5.1.5 on 2025-02-16 11:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0023_alter_transferrequest_retail_item_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='transferrequest',
            name='from_wholesale',
            field=models.BooleanField(default=False, help_text="True if request initiated by wholesale (targeting retail's stock), False if by retail."),
        ),
        migrations.AlterField(
            model_name='transferrequest',
            name='retail_item',
            field=models.ForeignKey(blank=True, help_text='Set when request originates from wholesale (to retail).', null=True, on_delete=django.db.models.deletion.CASCADE, to='store.item'),
        ),
        migrations.AlterField(
            model_name='transferrequest',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('received', 'Received')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='transferrequest',
            name='wholesale_item',
            field=models.ForeignKey(blank=True, help_text='Set when request originates from retail (to wholesale).', null=True, on_delete=django.db.models.deletion.CASCADE, to='store.wholesaleitem'),
        ),
    ]

# Generated by Django 5.1.7 on 2025-03-09 11:58

import datetime
import django.db.models.deletion
import shortuuid.django_fields
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0001_initial'),
        ('store', '0036_alter_receipt_sales_alter_salesitem_quantity_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='receipt',
            options={},
        ),
        migrations.AlterModelOptions(
            name='wholesalereceipt',
            options={},
        ),
        migrations.RemoveField(
            model_name='receipt',
            name='has_returns',
        ),
        migrations.RemoveField(
            model_name='receipt',
            name='last_modified',
        ),
        migrations.RemoveField(
            model_name='receipt',
            name='return_notes',
        ),
        migrations.RemoveField(
            model_name='wholesalereceipt',
            name='has_returns',
        ),
        migrations.RemoveField(
            model_name='wholesalereceipt',
            name='last_modified',
        ),
        migrations.RemoveField(
            model_name='wholesalereceipt',
            name='printed',
        ),
        migrations.RemoveField(
            model_name='wholesalereceipt',
            name='return_notes',
        ),
        migrations.AlterField(
            model_name='receipt',
            name='buyer_address',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.customer'),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='date',
            field=models.DateTimeField(default=datetime.datetime.now),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='payment_method',
            field=models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer')], default='Cash', max_length=20),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='receipt_id',
            field=shortuuid.django_fields.ShortUUIDField(alphabet='1234567890', length=5, max_length=50, prefix='', unique=True),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='sales',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='receipts', to='store.sales'),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='status',
            field=models.CharField(choices=[('Paid', 'Paid'), ('Unpaid', 'Unpaid')], default='Unpaid', max_length=20),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.0'), max_digits=10),
        ),
        migrations.AlterField(
            model_name='salesitem',
            name='quantity',
            field=models.IntegerField(),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='buyer_address',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='date',
            field=models.DateTimeField(default=datetime.datetime.now),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='payment_method',
            field=models.CharField(choices=[('Cash', 'Cash'), ('Wallet', 'Wallet'), ('Transfer', 'Transfer')], default='Cash', max_length=20),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='receipt_id',
            field=shortuuid.django_fields.ShortUUIDField(alphabet='1234567890', length=5, max_length=50, prefix='', unique=True),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='sales',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='wholesale_receipts', to='store.sales'),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='status',
            field=models.CharField(choices=[('Paid', 'Paid'), ('Unpaid', 'Unpaid')], default='Unpaid', max_length=20),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.0'), max_digits=10),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='wholesale_customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.wholesalecustomer'),
        ),
    ]

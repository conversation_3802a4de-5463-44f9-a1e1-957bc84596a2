{% extends "partials/base.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header">
            <h2 class="text-center mb-0">Register New User</h2>
        </div>
        <div class="card-body">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}

            <form method="POST" action="{% url 'userauth:register' %}" class="needs-validation" novalidate>
                {% csrf_token %}

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="id_full_name" class="form-label">Full Name</label>
                        {{ form.full_name }}
                        {% if form.full_name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.full_name.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="id_username" class="form-label">Username</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.username.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="id_mobile" class="form-label">Mobile Number</label>
                        {{ form.mobile }}
                        {% if form.mobile.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.mobile.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="id_email" class="form-label">Email (Optional)</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.email.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="id_user_type" class="form-label">User Type</label>
                        {{ form.user_type }}
                        {% if form.user_type.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.user_type.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="id_department" class="form-label">Department (Optional)</label>
                        {{ form.department }}
                        {% if form.department.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.department.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="id_employee_id" class="form-label">Employee ID (Optional)</label>
                        {{ form.employee_id }}
                        {% if form.employee_id.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.employee_id.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="id_hire_date" class="form-label">Hire Date (Optional)</label>
                        {{ form.hire_date }}
                        {% if form.hire_date.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.hire_date.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="id_password1" class="form-label">Password</label>
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.password1.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="id_password2" class="form-label">Confirm Password</label>
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.password2.errors|join:", " }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-success btn-lg">Register User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .container {
        max-width: 900px;
    }

    .form-control {
        height: 45px;
    }

    .btn-lg {
        height: 45px;
    }

    .invalid-feedback {
        display: block;
    }

    @media (max-width: 768px) {
        .container {
            padding: 15px;
        }
    }
</style>
{% endblock %}

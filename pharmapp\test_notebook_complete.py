#!/usr/bin/env python
"""
Comprehensive test script for the complete notebook functionality
This tests all features including the new enhancements
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse
from notebook.models import Note, NoteCategory
from django.utils import timezone
from datetime import timedelta
import json

User = get_user_model()

def test_complete_notebook_functionality():
    print("🧪 Comprehensive Notebook Functionality Test")
    print("=" * 60)
    
    # Setup test client and user
    client = Client()
    test_user, created = User.objects.get_or_create(
        username='notebook_test_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
    
    # Test 1: Authentication and Basic Access
    print("\n1. Testing Authentication and Basic Access...")
    try:
        # Test unauthenticated access
        response = client.get(reverse('notebook:dashboard'))
        assert response.status_code == 302, "Should redirect unauthenticated users"
        
        # Login
        login_success = client.login(username='notebook_test_user', password='testpass123')
        assert login_success, "Login should succeed"
        
        # Test authenticated access
        response = client.get(reverse('notebook:dashboard'))
        assert response.status_code == 200, "Dashboard should be accessible"
        assert 'Notebook Dashboard' in response.content.decode(), "Dashboard should contain title"
        
        print("   ✅ Authentication and access control working")
        
    except Exception as e:
        print(f"   ❌ Authentication test failed: {e}")
        return False
    
    # Test 2: Note CRUD Operations
    print("\n2. Testing Note CRUD Operations...")
    try:
        # Create note via form
        work_category = NoteCategory.objects.filter(name='Work').first()
        note_data = {
            'title': 'Test CRUD Note',
            'content': 'This is a test note for CRUD operations.',
            'category': work_category.id if work_category else '',
            'priority': 'high',
            'tags': 'test, crud, functionality',
            'is_pinned': True,
        }
        
        response = client.post(reverse('notebook:note_create'), note_data)
        assert response.status_code == 302, "Note creation should redirect"
        
        # Verify note was created
        note = Note.objects.filter(title='Test CRUD Note', user=test_user).first()
        assert note is not None, "Note should be created"
        assert note.is_pinned == True, "Note should be pinned"
        assert 'test' in note.tags, "Tags should be saved"
        
        # Test note detail view
        response = client.get(reverse('notebook:note_detail', kwargs={'pk': note.pk}))
        assert response.status_code == 200, "Note detail should be accessible"
        assert note.title in response.content.decode(), "Note title should be displayed"
        
        # Test note edit
        edit_data = note_data.copy()
        edit_data['title'] = 'Updated CRUD Note'
        response = client.post(reverse('notebook:note_edit', kwargs={'pk': note.pk}), edit_data)
        assert response.status_code == 302, "Note edit should redirect"
        
        note.refresh_from_db()
        assert note.title == 'Updated CRUD Note', "Note title should be updated"
        
        print("   ✅ Note CRUD operations working")
        
    except Exception as e:
        print(f"   ❌ CRUD operations test failed: {e}")
        return False
    
    # Test 3: Quick Note Creation API
    print("\n3. Testing Quick Note Creation API...")
    try:
        # Get CSRF token
        response = client.get(reverse('notebook:dashboard'))
        csrf_token = client.cookies['csrftoken'].value
        
        # Test quick note creation
        quick_note_data = {
            'title': 'Quick Test Note',
            'content': 'This is a quick note created via API.'
        }
        
        response = client.post(
            reverse('notebook:quick_note_create'),
            quick_note_data,
            HTTP_X_CSRFTOKEN=csrf_token
        )
        assert response.status_code == 200, "Quick note API should respond"
        
        response_data = json.loads(response.content)
        assert response_data['success'] == True, "Quick note creation should succeed"
        
        # Verify quick note was created
        quick_note = Note.objects.filter(title='Quick Test Note', user=test_user).first()
        assert quick_note is not None, "Quick note should be created"
        assert quick_note.priority == 'medium', "Default priority should be medium"
        
        print("   ✅ Quick note creation API working")
        
    except Exception as e:
        print(f"   ❌ Quick note API test failed: {e}")
        return False
    
    # Test 4: Search Functionality
    print("\n4. Testing Search Functionality...")
    try:
        # Create additional notes for search testing
        Note.objects.create(
            title='Python Programming Notes',
            content='Learning Python is essential for development.',
            user=test_user,
            tags='python, programming, development'
        )
        
        Note.objects.create(
            title='Django Framework Guide',
            content='Django makes web development faster and easier.',
            user=test_user,
            tags='django, web, framework'
        )
        
        # Test search via note list
        response = client.get(reverse('notebook:note_list'), {'query': 'Python'})
        assert response.status_code == 200, "Search should work"
        assert 'Python Programming Notes' in response.content.decode(), "Search should find Python note"
        
        # Test search API
        response = client.get(reverse('notebook:note_search_api'), {'q': 'Django'})
        assert response.status_code == 200, "Search API should respond"
        
        search_data = json.loads(response.content)
        assert len(search_data['results']) > 0, "Search API should return results"
        assert any('Django' in result['title'] for result in search_data['results']), "Should find Django note"
        
        print("   ✅ Search functionality working")
        
    except Exception as e:
        print(f"   ❌ Search functionality test failed: {e}")
        return False
    
    # Test 5: Tag-based Filtering
    print("\n5. Testing Tag-based Filtering...")
    try:
        # Test tag filtering
        response = client.get(reverse('notebook:notes_by_tag', kwargs={'tag': 'python'}))
        assert response.status_code == 200, "Tag filtering should work"
        assert 'python' in response.content.decode().lower(), "Tag should be mentioned in response"
        
        print("   ✅ Tag-based filtering working")
        
    except Exception as e:
        print(f"   ❌ Tag filtering test failed: {e}")
        return False
    
    # Test 6: Note Actions (Pin, Archive, Delete)
    print("\n6. Testing Note Actions...")
    try:
        test_note = Note.objects.filter(user=test_user).first()
        
        # Test pin toggle
        original_pinned = test_note.is_pinned
        response = client.get(reverse('notebook:note_pin', kwargs={'pk': test_note.pk}))
        assert response.status_code == 302, "Pin action should redirect"
        
        test_note.refresh_from_db()
        assert test_note.is_pinned != original_pinned, "Pin status should toggle"
        
        # Test archive toggle
        original_archived = test_note.is_archived
        response = client.get(reverse('notebook:note_archive', kwargs={'pk': test_note.pk}))
        assert response.status_code == 302, "Archive action should redirect"
        
        test_note.refresh_from_db()
        assert test_note.is_archived != original_archived, "Archive status should toggle"
        
        print("   ✅ Note actions working")
        
    except Exception as e:
        print(f"   ❌ Note actions test failed: {e}")
        return False
    
    # Test 7: Categories
    print("\n7. Testing Categories...")
    try:
        # Test category list
        response = client.get(reverse('notebook:category_list'))
        assert response.status_code == 200, "Category list should be accessible"
        
        # Test category creation
        category_data = {
            'name': 'Test Category',
            'description': 'A test category for testing',
            'color': '#ff5733'
        }
        
        response = client.post(reverse('notebook:category_create'), category_data)
        assert response.status_code == 302, "Category creation should redirect"
        
        # Verify category was created
        test_category = NoteCategory.objects.filter(name='Test Category').first()
        assert test_category is not None, "Category should be created"
        assert test_category.color == '#ff5733', "Category color should be saved"
        
        print("   ✅ Categories working")
        
    except Exception as e:
        print(f"   ❌ Categories test failed: {e}")
        return False
    
    # Test 8: Dashboard Statistics
    print("\n8. Testing Dashboard Statistics...")
    try:
        response = client.get(reverse('notebook:dashboard'))
        content = response.content.decode()
        
        # Check for statistics elements
        assert 'Total Notes' in content, "Should show total notes statistic"
        assert 'Pinned Notes' in content, "Should show pinned notes statistic"
        assert 'High Priority' in content, "Should show high priority statistic"
        assert 'Categories Used' in content, "Should show categories statistic"
        
        print("   ✅ Dashboard statistics working")
        
    except Exception as e:
        print(f"   ❌ Dashboard statistics test failed: {e}")
        return False
    
    # Test 9: Archived Notes View
    print("\n9. Testing Archived Notes View...")
    try:
        response = client.get(reverse('notebook:archived_notes'))
        assert response.status_code == 200, "Archived notes view should be accessible"
        
        print("   ✅ Archived notes view working")
        
    except Exception as e:
        print(f"   ❌ Archived notes test failed: {e}")
        return False
    
    # Test 10: User Isolation
    print("\n10. Testing User Isolation...")
    try:
        # Create another user
        other_user = User.objects.create_user(
            username='other_test_user',
            email='<EMAIL>',
            password='otherpass123'
        )
        
        # Create note for other user
        other_note = Note.objects.create(
            title='Other User Note',
            content='This note belongs to another user.',
            user=other_user
        )
        
        # Try to access other user's note
        response = client.get(reverse('notebook:note_detail', kwargs={'pk': other_note.pk}))
        assert response.status_code == 302, "Should redirect when accessing other user's note"
        
        print("   ✅ User isolation working")
        
    except Exception as e:
        print(f"   ❌ User isolation test failed: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 All comprehensive tests passed!")
    print("\nNotebook feature is fully functional with:")
    print("✅ Complete CRUD operations")
    print("✅ Advanced search and filtering")
    print("✅ Tag-based organization")
    print("✅ Quick note creation")
    print("✅ Category management")
    print("✅ Dashboard with statistics")
    print("✅ User isolation and security")
    print("✅ Note actions (pin, archive, delete)")
    print("✅ Responsive UI integration")
    
    return True

if __name__ == '__main__':
    success = test_complete_notebook_functionality()
    sys.exit(0 if success else 1)

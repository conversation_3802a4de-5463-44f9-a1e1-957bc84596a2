{% block content %}
<style>
  /* Style for manual price override */
  .manual-override-active {
    background-color: #fffacd; /* Light yellow background */
    border: 1px solid #ffd700; /* Gold border */
    font-weight: bold;
  }

  /* Style for the override checkbox */
  .price-override-checkbox:checked + label {
    font-weight: bold;
    color: #0056b3;
  }

  /* Style for the calculated price when override is active */
  .override-active .calculated-price {
    text-decoration: line-through;
    color: #6c757d;
  }
</style>
<div class="container mt-4" id="transfer-messages">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2>Transfer Store Items</h2>
  </div>
  <div class="mt-3">
    {% for message in messages %}
      <div style="text-align: center;" class="alert alert-{{ message.tags }}">{{ message }}</div>
    {% endfor %}
  </div>
  <form id="transfer-form" method="post"
        hx-post="{% url 'store:transfer_multiple_store_items' %}"
        hx-target="#transfer-messages"
        hx-swap="outerHTML">
    {% csrf_token %}
    <table class="table shadow table-hover table-responsive">
      <thead class="table-secondary">
        <tr>
          <th>
            <input type="checkbox" id="select-all-checkbox" title="Select All">
            <label for="select-all-checkbox" class="ml-1">Select All</label>
          </th>
          <th>Item Name</th>
          <th>D/form</th>
          <th>Brand</th>
          <th>Unit</th>
          <th>Stock Qty</th>
          <th>Qty to Transfer</th>
          <th>Transfer Unit</th>
          <th>Unit Conversion</th>
          <th>Markup (%)</th>
          <th>Price Override</th>
          <th>Destination</th>
        </tr>
      </thead>
      <tbody id="store-items">
        {% for item in store_items %}
        <tr id="store-item-{{ item.id }}">
          <td>
            <input type="checkbox" name="select_{{ item.id }}" class="item-checkbox">
            <input type="hidden" name="cost_{{ item.id }}" value="{{ item.cost_price }}">
          </td>
          <td>{{ item.name|upper }}</td>
          <td>{{ item.dosage_form }}</td>
          <td>{{ item.brand }}</td>
          <td>{{ item.unit }}</td>
          <td>{{ item.stock }}</td>
          <td>
            <input type="number" name="quantity_{{ item.id }}" style="width:100px;" class="form-control" min="0" value="0" step="0.5">
          </td>
          <td>
            <select name="transfer_unit_{{ item.id }}" style="width:130px;" class="form-control unit-select" data-item-id="{{ item.id }}">
              <option value="{{ item.unit }}" selected>{{ item.unit }}</option>
              {% for unit_value, unit_display in unit_choices %}
                {% if unit_value != item.unit %}
                  <option value="{{ unit_value }}">{{ unit_display }}</option>
                {% endif %}
              {% endfor %}
            </select>
          </td>
          <td>
            <input type="number" name="unit_conversion_{{ item.id }}" style="width:100px;" class="form-control unit-conversion" min="1" value="1" step="0.01">
            <small class="text-muted conversion-label">1 {{ item.unit }} = <span class="conversion-value">1</span> <span class="target-unit">{{ item.unit }}</span></small>
          </td>
          <td>
            <select name="markup_{{ item.id }}" style="width:150px;" class="form-control markup-select" data-item-id="{{ item.id }}" onchange="triggerPriceCalculation(this)">
              <option value="0">Select Markup</option>
              <option value="2.5">2.5% markup</option>
              <option value="5">5% markup</option>
              <option value="7.5">7.5% markup</option>
              <option value="10">10% markup</option>
              <option value="12.5">12.5% markup</option>
              <option value="15">15% markup</option>
              <option value="17.5">17.5% markup</option>
              <option value="20">20% markup</option>
              <option value="22.5">22.5% markup</option>
              <option value="25">25% markup</option>
              <option value="27.5">27.5% markup</option>
              <option value="30">30% markup</option>
              <option value="32.5">32.5% markup</option>
              <option value="35">35% markup</option>
              <option value="37.5">37.5% markup</option>
              <option value="40">40% markup</option>
              <option value="42.5">42.5% markup</option>
              <option value="45">45% markup</option>
              <option value="47.5">47.5% markup</option>
              <option value="50">50% markup</option>
              <option value="52.5">52.5% markup</option>
              <option value="55">55% markup</option>
              <option value="57.5">57.5% markup</option>
              <option value="60">60% markup</option>
              <option value="62.5">62.5% markup</option>
              <option value="65">65% markup</option>
              <option value="67.5">67.5% markup</option>
              <option value="70">70% markup</option>
              <option value="72.5">72.5% markup</option>
              <option value="75">75% markup</option>
              <option value="77.5">77.5% markup</option>
              <option value="80">80% markup</option>
              <option value="82.5">82.5% markup</option>
              <option value="85">85% markup</option>
              <option value="87.5">87.5% markup</option>
              <option value="90">90% markup</option>
              <option value="92.5">92.5% markup</option>
              <option value="95">95% markup</option>
              <option value="97.5">97.5% markup</option>
              <option value="100">100% markup</option>
            </select>
          </td>
          <td>
            <div class="form-check mb-1">
              <input type="checkbox" class="form-check-input price-override-checkbox" id="price_override_{{ item.id }}" name="price_override_{{ item.id }}">
              <label class="form-check-label" for="price_override_{{ item.id }}">Override</label>
            </div>
            <div class="mb-1">
              <small class="text-muted">Calculated: <span class="calculated-price" id="calculated_price_{{ item.id }}">0.00</span></small>
            </div>
            <div class="input-group" style="width:150px;">
              <input type="number" name="manual_price_{{ item.id }}" class="form-control manual-price" min="0" value="0" step="0.01" placeholder="Enter selling price">
              <div class="input-group-append">
                <span class="input-group-text">₦</span>
              </div>
            </div>
            <small class="text-muted d-block mt-1">Enter price to override calculated value</small>
          </td>
          <td>
            <select name="destination_{{ item.id }}" class="form-control" style="width:130px;">
              <option value="">Select</option>
              <option value="retail">Retail</option>
              <option value="wholesale">Wholesale</option>
            </select>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
    <button type="submit" class="btn btn-sm btn-primary">Process Transfers</button>
  </form>
</div>

<script>
  // Global function to trigger price calculation from inline event handlers
  function triggerPriceCalculation(selectElement) {
    console.log('Direct markup change triggered for:', selectElement.name);
    console.log('New value:', selectElement.value);
    const row = selectElement.closest('tr');
    if (row) {
      // Get the item ID from the select name
      const itemId = selectElement.name.split('_')[1];
      console.log('Item ID:', itemId);

      // Get the cost input
      const costInput = row.querySelector('input[name="cost_' + itemId + '"]');
      console.log('Cost input value:', costInput ? costInput.value : 'not found');

      // Calculate the selling price
      calculateSellingPrice(row);
    } else {
      console.error('Could not find row for select element:', selectElement);
    }
  }

  // Global function to trigger price calculation from inline event handlers
  function triggerPriceCalculation(selectElement) {
    console.log('Direct markup change triggered for:', selectElement.name);
    console.log('New value:', selectElement.value);
    const row = selectElement.closest('tr');
    if (row) {
      // Get the item ID from the select name
      const itemId = selectElement.name.split('_')[1];
      console.log('Item ID:', itemId);

      // Get the cost input
      const costInput = row.querySelector('input[name="cost_' + itemId + '"]');
      console.log('Cost input value:', costInput ? costInput.value : 'not found');

      // Calculate the selling price
      calculateSellingPrice(row);
    } else {
      console.error('Could not find row for select element:', selectElement);
    }
  }

  // Define the calculateSellingPrice function globally with full implementation
  window.calculateSellingPrice = function(row) {
    console.log('Global calculateSellingPrice called with row:', row);

    if (!row) {
      console.error('No row provided to calculateSellingPrice');
      return;
    }

    // Get the item ID from the select checkbox
    const selectCheckbox = row.querySelector('input[name^="select_"]');
    if (!selectCheckbox) {
      console.error('No select checkbox found in row');
      return;
    }

    const itemId = selectCheckbox.name.split('_')[1];
    console.log('Calculating price for item ID:', itemId);

    // Get the required elements
    const costInput = row.querySelector('input[name="cost_' + itemId + '"]');
    const markupSelect = row.querySelector('select[name="markup_' + itemId + '"]');
    const calculatedPriceSpan = row.querySelector('#calculated_price_' + itemId);
    const manualPriceInput = row.querySelector('input[name="manual_price_' + itemId + '"]');
    const unitConversionInput = row.querySelector('input[name="unit_conversion_' + itemId + '"]');
    const overrideCheckbox = row.querySelector('.price-override-checkbox');

    console.log('Cost input:', costInput);
    console.log('Markup select:', markupSelect);
    console.log('Calculated price span:', calculatedPriceSpan);

    if (costInput && markupSelect && calculatedPriceSpan) {
      console.log('Cost input value:', costInput.value);
      console.log('Markup select value:', markupSelect.value);

      // Parse the cost value
      let cost = 0;
      try {
        cost = parseFloat(costInput.value.replace(/,/g, '')) || 0;
      } catch (e) {
        console.error('Error parsing cost:', e);
        cost = 0;
      }

      // Parse the markup value
      const markup = parseFloat(markupSelect.value) || 0;

      // Parse the unit conversion value
      const unitConversion = parseFloat(unitConversionInput?.value || 1);

      console.log('Parsed cost:', cost);
      console.log('Parsed markup:', markup);
      console.log('Parsed unit conversion:', unitConversion);

      // Adjust cost based on unit conversion
      let adjustedCost = cost;
      if (unitConversion !== 1) {
        adjustedCost = cost / unitConversion;
      }

      console.log('Adjusted cost:', adjustedCost);

      // Calculate selling price
      const sellingPrice = adjustedCost + (adjustedCost * markup / 100);

      console.log('Calculated selling price:', sellingPrice);

      // Update the calculated price display
      calculatedPriceSpan.textContent = sellingPrice.toFixed(2);

      // Get the current manual price value
      const currentManualPrice = parseFloat(manualPriceInput.value) || 0;

      // If the manual price is 0 or matches the previous calculated price, update it
      // This ensures we don't overwrite a user's manual entry
      if (currentManualPrice === 0 || Math.abs(currentManualPrice - sellingPrice) < 0.01) {
        manualPriceInput.value = sellingPrice.toFixed(2);
      }

      // Always check the override checkbox to ensure the manual price is used
      overrideCheckbox.checked = true;
    } else {
      console.error('Missing required elements for calculation');
      if (!costInput) console.error('Cost input missing');
      if (!markupSelect) console.error('Markup select missing');
      if (!calculatedPriceSpan) console.error('Calculated price span missing');
    }
  };

  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded and parsed');
    // Select All checkbox functionality
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const itemCheckboxes = document.querySelectorAll("input[type='checkbox'][name^='select_']");

    // Add event listener to the "Select All" checkbox
    selectAllCheckbox.addEventListener('change', function() {
      const isChecked = this.checked;

      // Update all item checkboxes
      itemCheckboxes.forEach(function(checkbox) {
        checkbox.checked = isChecked;
      });
    });

    // Add event listeners to individual checkboxes to update "Select All" state
    itemCheckboxes.forEach(function(checkbox) {
      checkbox.addEventListener('change', updateSelectAllCheckbox);
    });

    // Function to update the "Select All" checkbox based on individual checkboxes
    function updateSelectAllCheckbox() {
      const allChecked = Array.from(itemCheckboxes).every(checkbox => checkbox.checked);
      const someChecked = Array.from(itemCheckboxes).some(checkbox => checkbox.checked);

      selectAllCheckbox.checked = allChecked;
      selectAllCheckbox.indeterminate = someChecked && !allChecked;
    }

    // Form submission handler
    document.getElementById("transfer-form").addEventListener("submit", function(e) {
      const checkboxes = document.querySelectorAll("input[type='checkbox'][name^='select_']");
      checkboxes.forEach(function(checkbox) {
        if (!checkbox.checked) {
          const row = checkbox.closest("tr");
          if (row) {
            row.querySelectorAll("input, select").forEach(function(el) {
              el.disabled = true;
            });
          }
        }
      });
    });

    // Handle unit selection change
    const unitSelects = document.querySelectorAll('.unit-select');
    unitSelects.forEach(function(select) {
      select.addEventListener('change', updateConversionLabel);
    });

    // Handle conversion value change
    const conversionInputs = document.querySelectorAll('.unit-conversion');
    conversionInputs.forEach(function(input) {
      input.addEventListener('input', function() {
        updateConversionLabel.call(this);
        // Also recalculate the price when conversion changes
        setTimeout(() => {
          const row = this.closest('tr');
          calculateSellingPrice(row);
        }, 100); // Small delay to allow conversion to update
      });
    });

    // Initial update of all conversion labels
    unitSelects.forEach(updateConversionLabel);

    // The calculateSellingPrice function is now defined globally

    // Calculate initial selling prices
    setTimeout(() => {
      console.log('Calculating initial selling prices');
      document.querySelectorAll('tr').forEach(function(row) {
        if (row.querySelector('input[name^="select_"]')) {
          // Set a default markup value if none is selected
          const itemId = row.querySelector('input[name^="select_"]').name.split('_')[1];
          const markupSelect = row.querySelector('select[name="markup_' + itemId + '"]');

          // Log the current state of the markup select
          console.log('Initial markup select value for item', itemId, ':', markupSelect ? markupSelect.value : 'not found');

          // Calculate the selling price
          calculateSellingPrice(row);
        }
      });
    }, 500); // Delay to ensure all elements are loaded

    // Recalculate selling price when markup changes
    document.querySelectorAll('select[name^="markup_"]').forEach(function(select) {
      select.addEventListener('change', function() {
        console.log('Markup changed to:', this.value);
        const row = this.closest('tr');
        // Force a small delay to ensure the markup value is updated
        setTimeout(() => {
          calculateSellingPrice(row);
        }, 50);
      });
    });

    // Recalculate selling price when unit changes
    document.querySelectorAll('select[name^="transfer_unit_"]').forEach(function(select) {
      select.addEventListener('change', function() {
        setTimeout(() => {
          const row = this.closest('tr');
          calculateSellingPrice(row);
        }, 100); // Small delay to allow unit conversion to update
      });
    });

    // Add event listeners to manual price inputs
    document.querySelectorAll('input[name^="manual_price_"]').forEach(function(input) {
      // Add input event listener to validate and format the manual price
      input.addEventListener('input', function() {
        const value = this.value;

        // Get the row and item ID
        const row = this.closest('tr');
        const itemId = row.id.replace('store-item-', '');

        // Always ensure the override checkbox is checked
        const overrideCheckbox = row.querySelector('.price-override-checkbox');
        overrideCheckbox.checked = true;

        // Ensure the value is a valid number
        if (value && !isNaN(parseFloat(value))) {
          // Format to 2 decimal places only when the user has finished typing
          // This prevents the cursor from jumping to the end while typing
          if (value.indexOf('.') !== -1 && value.split('.')[1].length > 2) {
            const formattedValue = parseFloat(value).toFixed(2);
            this.value = formattedValue;
          }
        }

        console.log('Manual price updated for item', itemId, 'to', this.value);
      });

      // Add focus event listener to select all text for easy editing
      input.addEventListener('focus', function() {
        // Select all text for easy replacement
        this.select();
      });
    });

    function updateConversionLabel() {
      const itemId = this.getAttribute('data-item-id');
      const row = this.closest('tr');

      // If called directly on page load, we need to get the itemId differently
      const actualItemId = itemId || this.querySelector('.unit-select')?.getAttribute('data-item-id');

      const sourceUnit = row.querySelector('td:nth-child(5)').textContent.trim();
      const targetUnit = row.querySelector('.unit-select').value;
      const conversionValue = row.querySelector('.unit-conversion').value;

      const conversionLabel = row.querySelector('.conversion-label');
      const conversionValueSpan = row.querySelector('.conversion-value');
      const targetUnitSpan = row.querySelector('.target-unit');

      conversionValueSpan.textContent = conversionValue;
      targetUnitSpan.textContent = targetUnit;
      conversionLabel.innerHTML = `1 ${sourceUnit} = <span class="conversion-value">${conversionValue}</span> <span class="target-unit">${targetUnit}</span>`;
    }

    // Final calculation of all prices to ensure they're displayed correctly
    setTimeout(() => {
      console.log('Final calculation of all prices');
      document.querySelectorAll('tr').forEach(function(row) {
        if (row.querySelector('input[name^="select_"]')) {
          calculateSellingPrice(row);
        }
      });
    }, 1000);
  });

  // Trigger calculations when the page is fully loaded
  window.onload = function() {
    console.log('Window fully loaded');
    // Calculate all prices again after window load
    document.querySelectorAll('tr').forEach(function(row) {
      if (row.querySelector('input[name^="select_"]')) {
        calculateSellingPrice(row);
      }
    });
  };
</script>
{% endblock %}

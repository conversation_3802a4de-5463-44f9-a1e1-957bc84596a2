# Generated by Django 5.1.5 on 2025-02-17 21:41

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0027_remove_storeitem_batch_number_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='storeitem',
            name='selling_price',
        ),
        migrations.AddField(
            model_name='storeitem',
            name='date',
            field=models.DateField(default=datetime.datetime.now),
        ),
        migrations.AddField(
            model_name='storeitem',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='store.supplier'),
        ),
    ]

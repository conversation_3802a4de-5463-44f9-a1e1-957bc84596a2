<!-- templates/wholesale/pending_wholesale_transfer_requests.html -->
{% extends "partials/base.html" %}
{% block content %}
<div class="container mt-4 table-responsive">
  {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }} mt-2 text-center">{{ message }}</div>
            {% endfor %}
          {% endif %}
  <h2>Pending Transfer Requests (Wholesale Side)</h2>
  {% if wholesale_pending_transfers %}
  <table class="table table-bordered table-hover shadow">
    <thead>
      <tr>
        <th>Item Name</th>
        <th>Item Unit</th>
        <th>Rqsted Qty</th>
        <th>Approved Qty</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for transfer in wholesale_pending_transfers %}
      <tr id="transfer-{{ transfer.id }}">
        <td>{{ transfer.wholesale_item.name }}</td>
        <td>{{ transfer.wholesale_item.unit }}</td>
        <td>{{ transfer.requested_quantity }}</td>
        <td>
          <!-- Inline form for approved quantity -->
          <form hx-post="{% url 'wholesale:wholesale_approve_transfer' transfer.id %}" 
                hx-target="#transfer-{{ transfer.id }}" 
                hx-swap="outerHTML"
                style="display: inline;">
            {% csrf_token %}
            <input type="number" 
                   name="approved_quantity" 
                   value="{{ transfer.requested_quantity }}" 
                   class="form-control d-inline-block" 
                   style="width: 100px;" 
                   min="1" 
                   max="{{ transfer.wholesale_item.stock }}"
                   required>
        </td>
        <td>
            <button type="submit" class="btn btn-sm btn-success">Approve</button>
          </form>
          <button type="button"
                  hx-post="{% url 'wholesale:reject_wholesale_transfer' transfer.id %}"
                  hx-target="#transfer-{{ transfer.id }}"
                  hx-swap="outerHTML"
                  class="btn btn-sm btn-danger ml-2">
            Reject
          </button>
          
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  {% else %}
    <p>No pending transfer requests.</p>
  {% endif %}
</div>
{% endblock %}

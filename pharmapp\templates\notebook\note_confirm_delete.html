{% extends 'notebook/base_notebook.html' %}
{% load static %}

{% block notebook_title %}Delete Note{% endblock %}
{% block notebook_subtitle %}Confirm note deletion{% endblock %}

{% block notebook_content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card shadow border-danger">
            <div class="card-header bg-danger text-white py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-exclamation-triangle"></i> Confirm Deletion
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h5 class="alert-heading">
                        <i class="fas fa-warning"></i> Warning!
                    </h5>
                    <p class="mb-0">
                        You are about to permanently delete this note. This action cannot be undone.
                    </p>
                </div>

                <div class="note-preview border-start border-danger border-3 ps-3 mb-4">
                    <h5 class="text-danger">{{ note.title }}</h5>
                    <p class="text-muted">
                        {{ note.content|truncatechars:200 }}
                    </p>
                    <small class="text-muted">
                        Created: {{ note.created_at|date:"M d, Y" }} | 
                        Last modified: {{ note.updated_at|timesince }} ago
                    </small>
                </div>

                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'notebook:note_detail' note.pk %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Yes, Delete Note
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

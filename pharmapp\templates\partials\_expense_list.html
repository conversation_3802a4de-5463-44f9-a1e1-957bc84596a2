<!-- Update the Generate Report button -->
{% comment %} <div class="mb-3">
    <a href="{% url 'store:generate_monthly_report' %}?month={% now 'Y-m' %}" class="btn btn-success">
        <i class="fas fa-file-alt"></i> Generate Expense Report
    </a>
</div> {% endcomment %}

<!-- Expense List Table -->
<div class="table-responsive">
<table class="table table-hover" id="expenseTable" width="100%" cellspacing="0">
    <thead class="table-dark">
        <tr>
            <th>Category</th>
            <th>Amount</th>
            <th>Date</th>
            <th>Description</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody id="expense-list">
        {% for expense in expenses %}
        <tr id="expense-{{ expense.id }}">
            <td>{{ expense.category }}</td>
            <td>{{ expense.amount }}</td>
            <td>{{ expense.date }}</td>
            <td>{{ expense.description }}</td>
            <td>
                <!-- Edit Expense Button (Triggers Modal) -->
                <button class="btn btn-info btn-sm" 
                        hx-get="{% url 'store:edit_expense_form' expense.id %}" 
                        hx-target="#modal-body" 
                        hx-trigger="click"
                        data-toggle="modal" 
                        data-target="#expenseModal">
                    Edit
                </button>

                <!-- Delete Expense -->
                <button class="btn btn-danger btn-sm" 
                        hx-post="{% url 'store:delete_expense' expense.id %}" 
                        hx-confirm="Are you sure you want to delete this entry?"
                        hx-target="#expense-list" 
                        hx-swap="innerHTML">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
</div>

<!-- Bootstrap Modal for Adding/Editing Expenses -->
<div class="modal fade" id="expenseModal" tabindex="-1" aria-labelledby="modalTitle" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Manage Expense</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">x</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form content will be loaded here dynamically with HTMX -->
            </div>
        </div>
    </div>
</div>

{% extends 'partials/base.html' %}

{% block content %}
<div class="container mt-4">
    <h2>Start Wholesale Stock Check</h2>

    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">Stock Check Options</h5>
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="zeroItems" name="zeroItems" checked>
                <label class="form-check-label" for="zeroItems">
                    Zero items with neither expected nor actual quantity
                </label>
            </div>

            <div class="mb-3">
                <label for="itemSearch" class="form-label">Search for specific items (optional):</label>
                <input type="text" class="form-control" id="itemSearch" name="q" placeholder="Enter item name, brand, or dosage form"
                    hx-get="{% url 'wholesale:search_wholesale_items' %}" hx-trigger="keyup changed delay:300ms"
                    hx-target="#searchResults" hx-indicator=".htmx-indicator">
                <div class="htmx-indicator" style="display:none">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <small>Searching...</small>
                </div>
                <div id="searchResults" class="mt-2"></div>
            </div>

            <form method="post" id="stockCheckForm">
                {% csrf_token %}
                <input type="hidden" name="selected_items" id="selectedItems" value="">
                <input type="hidden" name="zero_empty_items" id="zeroEmptyItems" value="true">
                <button class="btn btn-primary" type="submit">Create Stock Check</button>
            </form>
        </div>
    </div>
</div>

<script>
    // Update hidden field when checkbox changes
    document.getElementById('zeroItems').addEventListener('change', function() {
        document.getElementById('zeroEmptyItems').value = this.checked.toString();
    });

    // Item search functionality
    const selectedItemsInput = document.getElementById('selectedItems');
    const selectedItems = new Set();

    // Set up HTMX event listener for search results
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'searchResults') {
            // Format the search results as a list
            const results = event.detail.target;

            // If there are no items in the response, show a message
            if (!results.querySelector('tr')) {
                results.innerHTML = '<p>No items found</p>';
                return;
            }

            // Create a list from the items
            const items = Array.from(results.querySelectorAll('tr')).map(row => {
                const itemId = row.querySelector('input[type="checkbox"]')?.value;
                const itemName = row.querySelector('td:nth-child(2)')?.textContent;
                const dosageForm = row.querySelector('td:nth-child(3)')?.textContent;

                if (!itemId || !itemName) return null;

                return { id: itemId, name: itemName, dosageForm: dosageForm };
            }).filter(item => item !== null);

            // Create a list to display the items
            const ul = document.createElement('ul');
            ul.className = 'list-group';

            items.forEach(item => {
                const li = document.createElement('li');
                li.className = 'list-group-item d-flex justify-content-between align-items-center';
                li.innerHTML = `
                    <span>${item.name} - ${item.dosageForm}</span>
                    <button type="button" class="btn btn-sm btn-primary add-item" data-id="${item.id}">Add</button>
                `;
                ul.appendChild(li);
            });

            results.innerHTML = '';
            results.appendChild(ul);

            // Add event listeners to the Add buttons
            document.querySelectorAll('.add-item').forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.getAttribute('data-id');
                    selectedItems.add(itemId);
                    selectedItemsInput.value = Array.from(selectedItems).join(',');
                    this.disabled = true;
                    this.textContent = 'Added';
                });
            });
        }
    });
</script>
{% endblock %}
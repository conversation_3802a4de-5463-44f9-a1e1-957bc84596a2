{% for sales_item in sales_items %}
<tr>
    <td>{{ sales_item.item.name }}</td>
    <td>{{ sales_item.quantity|floatformat:2 }}</td>
    <td>{{ sales_item.returned_quantity|default:0|floatformat:2 }}</td>
    <td data-available-quantity="{{ sales_item.quantity|sub:sales_item.returned_quantity|default:sales_item.quantity }}">
        {{ sales_item.quantity|sub:sales_item.returned_quantity|default:sales_item.quantity|floatformat:2 }}
    </td>
</tr>
{% endfor %}

<script>
function printReceipt(format) {
    const originalTitle = document.title;
    const receipt = document.getElementById('receipt');
    const originalWidth = receipt.style.width;

    // Store original display values of dosage form columns
    const dosageFormCells = document.querySelectorAll('.dosage-form-column');
    const originalDisplayValues = Array.from(dosageFormCells).map(cell => cell.style.display);

    if (format === 'thermal') {
        receipt.style.width = '80mm'; // Increased from 72mm
        receipt.style.margin = '0 auto';
        receipt.style.padding = '2mm';
        document.title = 'Thermal_' + originalTitle;

        // Add thermal-specific styles
        receipt.classList.add('thermal-print');
        receipt.classList.add('hide-dosage');
        receipt.style.fontSize = '9px';
        receipt.style.lineHeight = '1.1';

        // Hide dosage form columns
        dosageFormCells.forEach(cell => {
            cell.style.display = 'none';
        });

        // Ensure table fits within thermal width
        const table = receipt.querySelector('table');
        if (table) {
            table.style.width = '76mm'; // Increased from 68mm
            table.style.fontSize = '8px';
            table.style.tableLayout = 'fixed';
        }
    } else {
        // A4 format settings
        receipt.style.width = '210mm';
        document.title = 'A4_' + originalTitle;
        receipt.classList.remove('thermal-print');
        receipt.classList.remove('hide-dosage');

        // Show dosage form columns
        dosageFormCells.forEach((cell, index) => {
            cell.style.display = originalDisplayValues[index];
        });

        // Reset table styles
        const table = receipt.querySelector('table');
        if (table) {
            table.style.width = '100%';
            table.style.fontSize = '';
            table.style.tableLayout = '';
        }
    }

    window.print();

    // Restore original styles after printing
    setTimeout(() => {
        receipt.style.width = originalWidth;
        receipt.style.margin = '';
        receipt.style.padding = '';
        receipt.classList.remove('thermal-print');
        receipt.classList.remove('hide-dosage');
        document.title = originalTitle;

        // Restore dosage form columns
        dosageFormCells.forEach((cell, index) => {
            cell.style.display = originalDisplayValues[index];
        });

        // Reset all modified styles
        const table = receipt.querySelector('table');
        if (table) {
            table.style.width = '';
            table.style.fontSize = '';
            table.style.tableLayout = '';
        }
    }, 100);
}
</script>

# Generated by Django 5.1.7 on 2025-06-13 06:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NoteCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('color', models.CharField(default='#007bff', help_text='Hex color code for category', max_length=7)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Note Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Note',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('is_pinned', models.BooleanField(default=False, help_text='Pin important notes to top')),
                ('is_archived', models.BooleanField(default=False, help_text='Archive completed notes')),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags for easy searching', max_length=500)),
                ('reminder_date', models.DateTimeField(blank=True, help_text='Optional reminder date', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='notebook.notecategory')),
            ],
            options={
                'ordering': ['-is_pinned', '-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='NoteShare',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('can_edit', models.BooleanField(default=False, help_text='Allow the recipient to edit the note')),
                ('shared_at', models.DateTimeField(auto_now_add=True)),
                ('note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shares', to='notebook.note')),
                ('shared_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shared_notes', to=settings.AUTH_USER_MODEL)),
                ('shared_with', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_notes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-shared_at'],
            },
        ),
        migrations.AddIndex(
            model_name='note',
            index=models.Index(fields=['user', '-updated_at'], name='notebook_no_user_id_067a9c_idx'),
        ),
        migrations.AddIndex(
            model_name='note',
            index=models.Index(fields=['user', 'category'], name='notebook_no_user_id_7d99ae_idx'),
        ),
        migrations.AddIndex(
            model_name='note',
            index=models.Index(fields=['user', 'is_pinned'], name='notebook_no_user_id_9c7857_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='noteshare',
            unique_together={('note', 'shared_with')},
        ),
    ]

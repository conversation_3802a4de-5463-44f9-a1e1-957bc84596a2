# Generated by Django 5.1.5 on 2025-02-06 14:36

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0003_stockdiscrepancy_discrepancy_value'),
    ]

    operations = [
        migrations.RenameField(
            model_name='stockcheck',
            old_name='user',
            new_name='created_by',
        ),
        migrations.RemoveField(
            model_name='stockcheck',
            name='check_date',
        ),
        migrations.RemoveField(
            model_name='stockcheck',
            name='notes',
        ),
        migrations.AddField(
            model_name='stockcheck',
            name='date',
            field=models.DateTimeField(default=datetime.datetime.now),
        ),
        migrations.AddField(
            model_name='stockcheck',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed')], default='pending', max_length=10),
        ),
        migrations.AlterField(
            model_name='stockcheck',
            name='id',
            field=models.AutoField(primary_key=True, serialize=False),
        ),
        migrations.CreateModel(
            name='StockCheckItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expected_quantity', models.PositiveIntegerField()),
                ('actual_quantity', models.PositiveIntegerField()),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.item')),
                ('stock_check', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.stockcheck')),
            ],
        ),
        migrations.DeleteModel(
            name='StockDiscrepancy',
        ),
    ]

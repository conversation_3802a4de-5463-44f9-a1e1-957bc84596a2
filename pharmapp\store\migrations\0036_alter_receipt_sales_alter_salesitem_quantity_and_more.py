# Generated by Django 5.1.7 on 2025-03-09 11:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0035_alter_receipt_options_alter_wholesalereceipt_options_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='receipt',
            name='sales',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='receipt', to='store.sales'),
        ),
        migrations.AlterField(
            model_name='salesitem',
            name='quantity',
            field=models.PositiveIntegerField(),
        ),
        migrations.AlterField(
            model_name='wholesalereceipt',
            name='sales',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='wholesale_receipt', to='store.sales'),
        ),
    ]
